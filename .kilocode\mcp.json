{"mcpServers": {"taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"AZURE_OPENAI_API_KEY": "f91a40e72e50419087f457cfa006f51a", "GOOGLE_API_KEY": "AIzaSyBrxtu8j1EvRQwsh3JaAdDMOt9n_KHzjVE", "OPENROUTER_API_KEY": "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", "OLLAMA_API_KEY": "", "MAX_TOKENS": 64000, "TEMPERATURE": 0.2, "DEFAULT_SUBTASKS": 5, "DEFAULT_PRIORITY": "medium"}}}}