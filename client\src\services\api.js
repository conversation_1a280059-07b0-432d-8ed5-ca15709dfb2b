import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3003';

const api = {
  // Start a new search
  startSearch: async (searchParams) => {
    try {
      const response = await axios.post(`${API_URL}/api/search`, searchParams);
      return response.data;
    } catch (error) {
      console.error('Error starting search:', error);
      throw error;
    }
  },

  // Get search status
  getSearchStatus: async (searchId) => {
    try {
      const response = await axios.get(`${API_URL}/api/search/${searchId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting search status:', error);
      throw error;
    }
  },

  // Get search results
  getSearchResults: async (searchId) => {
    try {
      const response = await axios.get(`${API_URL}/api/search/${searchId}/results`);
      return response.data;
    } catch (error) {
      console.error('Error getting search results:', error);
      throw error;
    }
  },

  // Cancel a search
  cancelSearch: async (searchId) => {
    try {
      const response = await axios.post(`${API_URL}/api/search/cancel/${searchId}`);
      return response.data;
    } catch (error) {
      console.error('Error cancelling search:', error);
      throw error;
    }
  }
};

export default api;
