const express = require('express');
const router = express.Router();
const businessController = require('../controllers/businessController');
const { validateBusinessId } = require('../middleware/validation');

// Get all businesses with pagination and filtering
router.get('/', businessController.getBusinesses);

// Get a specific business by ID
router.get('/:businessId', validateBusinessId, businessController.getBusiness);

module.exports = router;
