const { getEnv } = require('../../config/env');

/**
 * Get Hunter API key from environment variables
 * @returns {string} Hunter API key
 */
function getApiKey() {
  return getEnv('HUNTER_API_KEY', '');
}

/**
 * Check if Hunter API key is configured
 * @returns {boolean} True if API key is configured
 */
function isConfigured() {
  return !!getApiKey();
}

module.exports = {
  getApiKey,
  isConfigured
};
