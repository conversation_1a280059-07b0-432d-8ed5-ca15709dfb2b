const { getEnv } = require('../../config/env');

/**
 * WordPress Qualifier Module Configuration
 * This module doesn't require API keys as it analyzes HTML content directly
 */

/**
 * Get request timeout for website analysis
 * @returns {number} Timeout in milliseconds
 */
function getRequestTimeout() {
  return parseInt(getEnv('WORDPRESS_QUALIFIER_TIMEOUT', '10000'));
}

/**
 * Get maximum content size to analyze
 * @returns {number} Maximum content size in bytes
 */
function getMaxContentSize() {
  return parseInt(getEnv('WORDPRESS_QUALIFIER_MAX_SIZE', '5242880')); // 5MB default (increased from 1MB)
}

/**
 * Get user agent for requests
 * @returns {string} User agent string
 */
function getUserAgent() {
  return getEnv('WORDPRESS_QUALIFIER_USER_AGENT', 'Mozilla/5.0 (compatible; WordPressQualifier/1.0)');
}

/**
 * Check if module should follow redirects
 * @returns {boolean} Whether to follow redirects
 */
function shouldFollowRedirects() {
  return getEnv('WORDPRESS_QUALIFIER_FOLLOW_REDIRECTS', 'true') === 'true';
}

/**
 * Get maximum number of redirects to follow
 * @returns {number} Maximum redirects
 */
function getMaxRedirects() {
  return parseInt(getEnv('WORDPRESS_QUALIFIER_MAX_REDIRECTS', '5'));
}

module.exports = {
  getRequestTimeout,
  getMaxContentSize,
  getUserAgent,
  shouldFollowRedirects,
  getMaxRedirects
};
