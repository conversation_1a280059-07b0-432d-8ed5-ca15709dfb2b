# Business Finder - Installation Guide

This guide will help you set up and run the Business Finder application, which allows you to collect and store business information from Google Places API.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

1. [Docker](https://www.docker.com/get-started) and [Docker Compose](https://docs.docker.com/compose/install/)
2. A Google Places API key (required for searching businesses)

## Installation Steps

### 1. Extract the Files

Extract the downloaded zip file to a location of your choice.

### 2. Configure Environment Variables

1. Navigate to the extracted directory
2. Create a `.env` file in the root directory with the following content:

```
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
```

Replace `your_google_places_api_key_here` with your actual Google Places API key.

### 3. Start the Application

Open a terminal in the root directory of the project and run:

```bash
docker-compose up -d
```

This command will:
- Build and start the backend server
- Build and start the React frontend
- Set up a Redis database for storing business information

The first time you run this command, it may take several minutes to download and build all the necessary components.

### 4. Access the Application

Once all services are running, you can access the Business Finder application at:

- Frontend: [http://localhost:3005](http://localhost:3005)
- Backend API: [http://localhost:3003](http://localhost:3003)

## Using the Application

1. Enter search criteria in the form (keyword is required)
2. Set optional parameters like country, city, minimum rating, etc.
3. Click "Search" to start collecting business data
4. View real-time results in the dashboard
5. Use filters to refine the displayed results

## Manual Installation (Without Docker)

If you prefer to run the application without Docker, follow these steps:

### Backend Setup

1. Ensure you have Node.js (v14+) and Redis installed
2. Navigate to the `server` directory
3. Create a `.env` file with the following content:
   ```
   PORT=3003
   CLIENT_URL=http://localhost:3005
   REDIS_URL=redis://localhost:6379
   GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
   ```
4. Install dependencies:
   ```bash
   npm install
   ```
5. Start the server:
   ```bash
   node src/server.js
   ```

### Frontend Setup

1. Navigate to the `client` directory
2. Create a `.env` file with the following content:
   ```
   REACT_APP_API_URL=http://localhost:3003
   ```
3. Install dependencies:
   ```bash
   npm install
   ```
4. Start the development server:
   ```bash
   npm start
   ```

## Troubleshooting

### Connection Issues

If the frontend cannot connect to the backend:
1. Ensure all services are running (`docker-compose ps`)
2. Check that the backend is accessible at http://localhost:3003/health
3. Verify that the REACT_APP_API_URL is correctly set

### API Key Issues

If searches fail with API errors:
1. Verify your Google Places API key is correct
2. Ensure the Places API is enabled in your Google Cloud Console
3. Check for any quota limitations on your API key

## Stopping the Application

To stop the application, run:

```bash
docker-compose down
```

To stop and remove all data (including the Redis database), run:

```bash
docker-compose down -v
```

## Data Persistence

Business data is stored in a Redis database and persists between application restarts. The data is stored in Docker volumes, which remain even if the containers are stopped or removed.

## Additional Configuration

For advanced configuration options, refer to the `.env.example` files in both the `client` and `server` directories.
