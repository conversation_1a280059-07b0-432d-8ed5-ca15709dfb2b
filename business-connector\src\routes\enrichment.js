const express = require('express');
const router = express.Router();
const enrichmentController = require('../controllers/enrichmentController');
const { validateBusinessId, validateModuleName, validateEnrichmentData } = require('../middleware/validation');

// Get all available enrichment modules
router.get('/modules', enrichmentController.listAvailableModules); // Changed from getModules to listAvailableModules

// Get all enrichment data for a business
router.get('/:businessId', validateBusinessId, enrichmentController.getBusinessEnrichment);

// Get specific module enrichment for a business
router.get('/:businessId/:module', validateBusinessId, validateModuleName, enrichmentController.getModuleEnrichment);

// Run enrichment for a business using a specific module
router.post('/:businessId/:module', validateBusinessId, validateModuleName, enrichmentController.runEnrichment);

// Update enrichment data for a business
router.put('/:businessId/:module', validateBusinessId, validateModuleName, validateEnrichmentData, enrichmentController.updateEnrichment);

module.exports = router;
