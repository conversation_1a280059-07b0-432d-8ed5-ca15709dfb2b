/**
 * Business Connector Installation Guide
 * 
 * This file provides step-by-step instructions for installing and configuring
 * the Business Connector application.
 */

# Business Connector Installation Guide

## Prerequisites

- Node.js 14+
- Redis server (running and accessible)
- Hunter API key (for email enrichment)

## Installation Steps

### 1. Extract the Package

Extract the `business-connector.zip` file to your desired location.

### 2. Configure Environment Variables

Create a `.env` file in the root directory with the following variables:

```
PORT=3006
REDIS_URL=redis://localhost:6379
HUNTER_API_KEY=your_hunter_api_key
NODE_ENV=production
LOG_LEVEL=info
```

Replace `your_hunter_api_key` with your actual Hunter API key. If you don't have one, you can get a free API key at [hunter.io](https://hunter.io).

### 3. Install Dependencies

Open a terminal in the extracted directory and run:

```
npm install
```

This will install all required dependencies.

### 4. Start the Application

Start the application using:

```
npm start
```

The Business Connector should now be running on port 3006.

### 5. Verify Installation

Open a web browser and navigate to:

```
http://localhost:3006/api/enrichment/modules
```

You should see a JSON response listing the available enrichment modules.

## Docker Installation (Alternative)

If you prefer to use Docker, follow these steps:

### 1. Create Docker Environment File

Create a `.env` file as described in step 2 above.

### 2. Build the Docker Image

```
docker build -t business-connector .
```

### 3. Run the Container

```
docker run -p 3006:3006 --env-file .env business-connector
```

### 4. Verify Installation

As in step 5 above, navigate to `http://localhost:3006/api/enrichment/modules` to verify the installation.

## Integration with Existing Applications

The Business Connector is designed to work with your existing Redis database. It will:

1. Read business records from your existing Redis keys (`business:{id}`)
2. Store enrichment data in separate namespaced keys (`connector:enrichment:{business_id}:{module}`)

No changes are required to your existing applications to use the enrichment data.

## Troubleshooting

### Common Issues

- **Redis Connection Error**: Verify that your Redis server is running and accessible at the URL specified in the `.env` file.
- **Module Configuration Error**: Ensure that your Hunter API key is valid and correctly set in the `.env` file.
- **Port Conflict**: If port 3006 is already in use, change the `PORT` environment variable to an available port.

### Validation

To validate the installation, run:

```
node src/tests/validate.js
```

This will test Redis connectivity, module loading, API endpoints, and error handling.

## Next Steps

Once installed, you can:

1. Access the API endpoints to view and enrich business data
2. Develop additional enrichment modules
3. Integrate enrichment data into your existing applications

For more information, see the `DOCUMENTATION.md` file.
