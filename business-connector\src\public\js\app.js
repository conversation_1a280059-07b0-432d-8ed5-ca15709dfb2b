// Main application initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tab navigation
    const businessesTab = document.getElementById('businesses-tab');
    const modulesTab = document.getElementById('modules-tab');
    const logsTab = document.getElementById('logs-tab');
    
    const businessesContent = document.getElementById('businesses-content');
    const modulesContent = document.getElementById('modules-content');
    const logsContent = document.getElementById('logs-content');
    
    const sectionTitle = document.getElementById('section-title');
    
    // Set up tab click handlers
    businessesTab.addEventListener('click', function(e) {
        e.preventDefault();
        activateTab(businessesTab, businessesContent, 'Businesses');
    });
    
    modulesTab.addEventListener('click', function(e) {
        e.preventDefault();
        activateTab(modulesTab, modulesContent, 'Modules');
        
        // Load modules if not already loaded
        if (modules.length === 0) {
            loadModules();
        }
    });
    
    logsTab.addEventListener('click', function(e) {
        e.preventDefault();
        activateTab(logsTab, logsContent, 'Logs');
    });
    
    // Function to activate a tab
    function activateTab(tab, content, title) {
        // Update active tab
        businessesTab.classList.remove('active');
        modulesTab.classList.remove('active');
        logsTab.classList.remove('active');
        tab.classList.add('active');
        
        // Update visible content
        businessesContent.classList.add('d-none');
        modulesContent.classList.add('d-none');
        logsContent.classList.add('d-none');
        content.classList.remove('d-none');
        
        // Update section title
        sectionTitle.textContent = title;
    }
    
    // Initialize components
    initBusinesses();
    initModules();
    initLogs();
});
