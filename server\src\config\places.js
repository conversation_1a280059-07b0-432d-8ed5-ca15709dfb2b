// Google Places API configuration
const axios = require('axios');

const createPlacesClient = () => {
  const apiKey = process.env.GOOGLE_PLACES_API_KEY;
  
  if (!apiKey) {
    console.error('Google Places API key is not defined in environment variables');
    process.exit(1);
  }

  const placesClient = {
    textSearch: async (params) => {
      try {
        const response = await axios.get('https://maps.googleapis.com/maps/api/place/textsearch/json', {
          params: {
            ...params,
            key: apiKey
          }
        });
        return response.data;
      } catch (error) {
        console.error('Error in Google Places API text search:', error);
        throw error;
      }
    },
    
    getDetails: async (placeId) => {
      try {
        const response = await axios.get('https://maps.googleapis.com/maps/api/place/details/json', {
          params: {
            place_id: placeId,
            fields: 'name,formatted_address,formatted_phone_number,website,rating,user_ratings_total',
            key: api<PERSON><PERSON>
          }
        });
        return response.data;
      } catch (error) {
        console.error('Error in Google Places API details:', error);
        throw error;
      }
    },
    
    getNextPage: async (nextPageToken) => {
      try {
        // Wait a short time before requesting the next page as per Google's recommendation
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const response = await axios.get('https://maps.googleapis.com/maps/api/place/textsearch/json', {
          params: {
            pagetoken: nextPageToken,
            key: apiKey
          }
        });
        return response.data;
      } catch (error) {
        console.error('Error in Google Places API next page:', error);
        throw error;
      }
    }
  };

  return placesClient;
};

module.exports = { createPlacesClient };
