# Business Management Dashboard - Installation and Usage Guide

This guide provides instructions for installing, configuring, and using the Business Management Dashboard.

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Running the Application](#running-the-application)
5. [Usage Guide](#usage-guide)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

Before installing the Business Management Dashboard, ensure you have the following:

- Docker and Docker Compose installed
- Node.js 14.x or higher (for development only)
- Access to the existing Redis database used by the Business Finder application
- Modern web browser (Chrome, Firefox, Safari, or Edge)

## Installation

### Option 1: Using Docker Compose (Recommended)

1. Extract the Business Management Dashboard files to your server.

2. Navigate to the directory containing the `docker-compose.yml` file.

3. Update the `docker-compose.yml` file to include the Business Management Dashboard service:

```yaml
version: '3'

services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped

  backend:
    build:
      context: ./server
    ports:
      - "3003:3003"
    environment:
      - PORT=3003
      - CLIENT_URL=http://localhost:3005
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    restart: unless-stopped

  frontend:
    build:
      context: ./client
    ports:
      - "3005:3005"
    environment:
      - REACT_APP_API_URL=http://localhost:3003
    depends_on:
      - backend
    restart: unless-stopped

  business-management:
    build:
      context: ./business-management
    ports:
      - "3004:3004"
    environment:
      - PORT=3004
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your_jwt_secret_here
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  redis-data:
```

4. Build and start the services:

```bash
docker-compose up -d
```

### Option 2: Manual Installation (Development)

1. Extract the Business Management Dashboard files to your server.

2. Navigate to the business-management directory:

```bash
cd business-management
```

3. Install dependencies:

```bash
npm install
```

4. Create a `.env` file with the following content:

```
PORT=3004
REDIS_URL=redis://localhost:6379
JWT_SECRET=your_jwt_secret_here
```

5. Start the application:

```bash
npm start
```

## Configuration

### Environment Variables

The Business Management Dashboard can be configured using the following environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| PORT | Port on which the server will listen | 3004 |
| REDIS_URL | URL of the Redis server | redis://localhost:6379 |
| JWT_SECRET | Secret key for JWT token generation | business-management-secret |

### Authentication

The default user credentials are:

- Username: `admin`
- Password: `admin123`

For production use, you should modify the authentication system to use a more secure approach. The current implementation uses a simple in-memory user store for demonstration purposes.

## Running the Application

### Accessing the Dashboard

Once the application is running, you can access the Business Management Dashboard at:

```
http://localhost:3004
```

### Verifying Installation

To verify that the application is running correctly:

1. Navigate to the dashboard URL in your browser
2. Log in using the default credentials
3. Verify that you can see the list of businesses from the Redis database
4. Try searching, filtering, and editing a business to ensure functionality

## Usage Guide

### Logging In

1. Navigate to the dashboard URL in your browser
2. Enter your username and password
3. Click the "Login" button

### Viewing Businesses

The main dashboard displays a list of businesses with the following information:
- Name
- Address
- Phone
- Website
- Rating

You can:
- Navigate between pages using the pagination controls
- Search and filter businesses using the sidebar form
- Click on a business to view its details

### Editing Business Details

1. Click on a business in the list to view its details
2. Edit the information in the form
3. Click "Save Changes" to update the business

### Managing Notes

When viewing a business's details, you can:

1. View existing notes for the business
2. Add a new note by clicking the "Add Note" button
3. Edit a note by clicking the edit icon
4. Delete a note by clicking the edit icon and then the "Delete Note" button

### Real-time Updates

The dashboard automatically updates when:
- Another user edits a business you're viewing
- Another user adds, edits, or deletes a note for a business you're viewing
- Data is updated through the Business Finder application

### Logging Out

Click the "Logout" button in the top-right corner of the dashboard to log out.

## Troubleshooting

### Connection Issues

If you cannot connect to the dashboard:

1. Verify that the server is running
2. Check that you're using the correct URL and port
3. Ensure that the Redis server is accessible

### Redis Connection Errors

If the application cannot connect to Redis:

1. Verify that the Redis server is running
2. Check that the REDIS_URL environment variable is set correctly
3. Ensure that the Redis port is accessible from the application container

### Authentication Issues

If you cannot log in:

1. Verify that you're using the correct credentials
2. Check the server logs for authentication errors
3. Ensure that the JWT_SECRET environment variable is set

### Real-time Updates Not Working

If real-time updates are not functioning:

1. Check that your browser supports WebSockets
2. Verify that there are no network restrictions blocking WebSocket connections
3. Check the server logs for WebSocket connection errors

For additional support, please refer to the documentation or contact the development team.
