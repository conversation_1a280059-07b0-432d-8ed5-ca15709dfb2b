/**
 * Companies House API Configuration
 */

const { logger } = require('../../services/logService');

/**
 * Get Companies House API key from environment variables
 * @returns {string|null} API key or null if not configured
 */
function getApiKey() {
  return process.env.COMPANIES_HOUSE_UK_API || null;
}

/**
 * Get Companies House API base URL
 * @returns {string} Base URL for Companies House API
 */
function getBaseUrl() {
  return 'https://api.company-information.service.gov.uk';
}

/**
 * Get API request headers
 * @returns {Object} Headers for API requests
 */
function getHeaders() {
  const apiKey = getApiKey();
  if (!apiKey) {
    throw new Error('Companies House API key not configured');
  }

  return {
    'Authorization': `Basic ${Buffer.from(apiKey + ':').toString('base64')}`,
    'Content-Type': 'application/json',
    'User-Agent': 'Business-Connector/1.0.0'
  };
}

/**
 * Check if we're in mock mode (for testing without API key)
 * @returns {boolean} True if in mock mode
 */
function isMockMode() {
  const apiKey = getApiKey();
  return !apiKey || apiKey === 'YOUR_API_KEY_HERE' || apiKey === 'MOCK';
}

/**
 * Validate API configuration
 * @returns {boolean} True if configuration is valid
 */
function validateConfig() {
  const apiKey = getApiKey();

  if (!apiKey || apiKey === 'YOUR_API_KEY_HERE') {
    logger.warn('Companies House API key not configured - running in mock mode');
    return true; // Allow mock mode for testing
  }

  if (apiKey.length < 10) {
    logger.warn('Companies House API key appears to be invalid (too short)');
    return false;
  }

  return true;
}

module.exports = {
  getApiKey,
  getBaseUrl,
  getHeaders,
  validateConfig,
  isMockMode
};
