const express = require('express');
const router = express.Router();
const passport = require('passport');
const redisService = require('../services/redisService');

// Authentication middleware
const auth = passport.authenticate('jwt', { session: false });

// @route   GET api/businesses
// @desc    Get all businesses with pagination and filtering
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    console.log('GET /businesses request received with query:', req.query);
    
    const {
      page = 1,
      limit = 10,
      keyword,
      country,
      city,
      minRating,
      maxRating,
      minRatings,
      maxRatings,
      hasWebsite,
      hasPhone
    } = req.query;

    // Convert parameters
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const minRatingNum = minRating ? parseFloat(minRating) : undefined;
    const maxRatingNum = maxRating ? parseFloat(maxRating) : undefined;
    const minRatingsNum = minRatings ? parseInt(minRatings) : undefined;
    const maxRatingsNum = maxRatings ? parseInt(maxRatings) : undefined;
    const hasWebsiteBool = hasWebsite === 'true' ? true : hasWebsite === 'false' ? false : undefined;
    const hasPhoneBool = hasPhone === 'true' ? true : hasPhone === 'false' ? false : undefined;
    
    console.log('Parsed query parameters:', {
      pageNum,
      limitNum,
      keyword,
      country,
      city,
      minRatingNum,
      maxRatingNum,
      minRatingsNum,
      maxRatingsNum,
      hasWebsiteBool,
      hasPhoneBool
    });
    
    // Get businesses from Redis
    const businesses = await redisService.getBusinesses({
      page: pageNum,
      limit: limitNum,
      filters: {
        keyword,
        country,
        city,
        minRating: minRatingNum,
        maxRating: maxRatingNum,
        minRatings: minRatingsNum,
        maxRatings: maxRatingsNum,
        hasWebsite: hasWebsiteBool,
        hasPhone: hasPhoneBool
      }
    });
    
    console.log(`Found ${businesses.businesses?.length || 0} businesses`);
    
    res.json(businesses);
  } catch (err) {
    console.error('Error fetching businesses:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// @route   GET api/businesses/:id
// @desc    Get a specific business by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const business = await redisService.getBusinessById(req.params.id);
    
    if (!business) {
      return res.status(404).json({ message: 'Business not found' });
    }
    
    res.json(business);
  } catch (err) {
    console.error('Error fetching business:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT api/businesses/:id
// @desc    Update business details
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    let { name, address, phone, website, rating } = req.body;

    // Sanitize input: treat empty strings as undefined
    name = name && name.trim() !== '' ? name : undefined;
    address = address && address.trim() !== '' ? address : undefined;
    phone = phone && phone.trim() !== '' ? phone : undefined;
    website = website && website.trim() !== '' ? website : undefined;
    rating = rating !== undefined && rating !== '' ? parseFloat(rating) : undefined;

    // Validate at least one field
    if (!name && !address && !phone && !website && rating === undefined) {
      return res.status(400).json({ message: 'Please provide at least one field to update' });
    }

    // Check if business exists
    const existingBusiness = await redisService.getBusinessById(req.params.id);
    if (!existingBusiness) {
      return res.status(404).json({ message: 'Business not found' });
    }

    // Only update provided fields, keep others as-is
    const updatedBusiness = {
      ...existingBusiness,
      ...(name !== undefined && { name }),
      ...(address !== undefined && { address }),
      ...(phone !== undefined && { phone }),
      ...(website !== undefined && { website }),
      ...(rating !== undefined && { rating })
    };

    await redisService.updateBusiness(req.params.id, updatedBusiness);

    // Emit socket event for real-time updates
    req.io.emit('business:update', { id: req.params.id, business: updatedBusiness });

    res.json(updatedBusiness);
  } catch (err) {
    console.error('Error updating business:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE api/businesses/:id
// @desc    Delete a business
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if business exists
    const existingBusiness = await redisService.getBusinessById(req.params.id);
    if (!existingBusiness) {
      return res.status(404).json({ message: 'Business not found' });
    }
    
    // Delete business
    await redisService.deleteBusiness(req.params.id);
    
    // Emit socket event for real-time updates
    req.io.emit('business:delete', { id: req.params.id });
    
    res.json({ message: 'Business deleted successfully' });
  } catch (err) {
    console.error('Error deleting business:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
