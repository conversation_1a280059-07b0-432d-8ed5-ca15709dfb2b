// API client for Business Connector
const API_BASE_URL = '/api';

/**
 * Fetch businesses with pagination
 * @param {number} page - Page number
 * @param {number} limit - Number of records per page
 * @param {string} search - Search term (optional)
 * @returns {Promise<Object>} - Businesses and pagination info
 */
async function fetchBusinesses(page = 1, limit = 10, search = '') {
    try {
        let url = `${API_BASE_URL}/businesses?page=${page}&limit=${limit}`;
        
        if (search) {
            url += `&search=${encodeURIComponent(search)}`;
        }
        
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        
        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error('Error fetching businesses:', error);
        throw error;
    }
}

/**
 * Fetch a specific business by ID
 * @param {string} businessId - Business ID
 * @returns {Promise<Object>} - Business details and enrichment data
 */
async function fetchBusiness(businessId) {
    try {
        const response = await fetch(`${API_BASE_URL}/businesses/${businessId}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        
        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error(`Error fetching business ${businessId}:`, error);
        throw error;
    }
}

/**
 * Fetch all available enrichment modules
 * @returns {Promise<Array>} - List of modules
 */
async function fetchModules() {
    try {
        const response = await fetch(`${API_BASE_URL}/enrichment/modules`);
        
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        
        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error('Error fetching modules:', error);
        throw error;
    }
}

/**
 * Fetch enrichment data for a business
 * @param {string} businessId - Business ID
 * @param {string} module - Module name (optional)
 * @returns {Promise<Object>} - Enrichment data
 */
async function fetchEnrichmentData(businessId, module = null) {
    try {
        const url = module 
            ? `${API_BASE_URL}/enrichment/${businessId}/${module}`
            : `${API_BASE_URL}/enrichment/${businessId}`;
            
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        
        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error(`Error fetching enrichment data for business ${businessId}:`, error);
        throw error;
    }
}

/**
 * Run enrichment for a business using a specific module
 * @param {string} businessId - Business ID
 * @param {string} module - Module name
 * @returns {Promise<Object>} - Enrichment results
 */
async function runEnrichment(businessId, module) {
    try {
        const response = await fetch(`${API_BASE_URL}/enrichment/${businessId}/${module}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error ${response.status}`);
        }
        
        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error(`Error running enrichment for business ${businessId} with module ${module}:`, error);
        throw error;
    }
}

/**
 * Save enrichment data for a business
 * @param {string} businessId - Business ID
 * @param {string} module - Module name
 * @param {Object} data - Enrichment data to save
 * @returns {Promise<Object>} - Success message
 */
async function saveEnrichmentData(businessId, module, data) {
    try {
        const response = await fetch(`${API_BASE_URL}/enrichment/${businessId}/${module}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ data })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error ${response.status}`);
        }
        
        const responseData = await response.json();
        return responseData;
    } catch (error) {
        console.error(`Error saving enrichment data for business ${businessId} with module ${module}:`, error);
        throw error;
    }
}
