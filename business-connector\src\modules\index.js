const fs = require('fs').promises;
const path = require('path');
const { logger, logModuleError } = require('../services/logService');
const moduleInterface = require('./moduleInterface');

// Module registry
const modules = new Map();

/**
 * Load all modules from the modules directory
 * @returns {Promise<Map>} Map of module name to module object
 */
async function initializeModules() {
  try {
    const modulesDir = path.join(__dirname);
    const entries = await fs.readdir(modulesDir, { withFileTypes: true });
    
    for (const entry of entries) {
      // Skip non-directories and special directories
      if (!entry.isDirectory() || entry.name === 'node_modules' || entry.name === '.git') {
        continue;
      }
      
      try {
        // Skip the current directory and parent directory
        if (entry.name === '.' || entry.name === '..') {
          continue;
        }
        
        const modulePath = path.join(modulesDir, entry.name);
        
        // Check if the directory has an index.js file
        try {
          await fs.access(path.join(modulePath, 'index.js'));
        } catch (error) {
          logger.warn(`Module directory ${entry.name} does not contain an index.js file`);
          continue;
        }
        
        // Load the module
        const module = require(path.join(modulePath));
        
        // Validate module interface
        if (validateModuleInterface(module)) {
          modules.set(entry.name, module);
          logger.info(`Loaded module: ${module.metadata.name}`);
        } else {
          logger.warn(`Module ${entry.name} does not implement the required interface`);
        }
      } catch (error) {
        logModuleError(entry.name, `Error loading module: ${error.message}`);
      }
    }
    
    return modules;
  } catch (error) {
    logger.error('Error loading modules:', error);
    return new Map();
  }
}

/**
 * Validate that a module implements the required interface
 * @param {Object} module - Module to validate
 * @returns {boolean} True if the module implements the required interface
 */
function validateModuleInterface(module) {
  return (
    module.metadata &&
    typeof module.metadata === 'object' &&
    module.metadata.name &&
    typeof module.validateConfig === 'function' &&
    typeof module.enrich === 'function'
  );
}

/**
 * Get all loaded module names and their metadata
 * @returns {Array<Object>} Array of module metadata
 */
function getAvailableModules() {
  return Array.from(modules.values()).map(module => module.metadata);
}

/**
 * Get a specific module by name
 * @param {string} name - Module name
 * @returns {Object|null} Module object or null if not found
 */
function getModule(name) {
  return modules.get(name) || null;
}

/**
 * Validate all module configurations
 * @returns {Promise<Object>} Object mapping module names to validation status
 */
async function validateModuleConfigs() {
  const validationResults = {};
  
  for (const [name, module] of modules.entries()) {
    try {
      validationResults[name] = await module.validateConfig();
    } catch (error) {
      logModuleError(name, `Error validating module configuration: ${error.message}`);
      validationResults[name] = false;
    }
  }
  
  return validationResults;
}

module.exports = {
  initializeModules,
  getAvailableModules, // Added
  getModule,
  validateModuleConfigs
};
