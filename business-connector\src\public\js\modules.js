// Modules management and display
let modules = [];

/**
 * Initialize modules functionality
 */
function initModules() {
    // Load modules on initial page load
    loadModules();
}

/**
 * Load available modules from the API
 */
async function loadModules() {
    try {
        // Show loading state
        const modulesContainer = document.getElementById('modules-container');
        if (modulesContainer) {
            modulesContainer.innerHTML = '<div class="col-12 text-center"><i class="fas fa-spinner fa-spin"></i> Loading modules...</div>';
        }

        // Fetch modules from API
        const moduleData = await fetchModules();
        modules = moduleData || [];

        // Render modules
        renderModules();

        // Populate module select dropdown in enrichment tab
        populateModuleSelect();
    } catch (error) {
        console.error('Error loading modules:', error);
        const modulesContainer = document.getElementById('modules-container');
        if (modulesContainer) {
            modulesContainer.innerHTML = `<div class="col-12"><div class="alert alert-danger">Error loading modules: ${error.message}</div></div>`;
        }
    }
}

/**
 * Render modules in the modules container
 */
function renderModules() {
    const modulesContainer = document.getElementById('modules-container');

    if (!modulesContainer) {
        console.warn('Modules container not found');
        return;
    }

    if (modules.length === 0) {
        modulesContainer.innerHTML = '<div class="col-12 text-center">No modules available</div>';
        return;
    }

    modulesContainer.innerHTML = '';

    modules.forEach(module => {
        const moduleCard = document.createElement('div');
        moduleCard.className = 'col-md-6 col-lg-4 mb-3';

        moduleCard.innerHTML = `
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-puzzle-piece me-2"></i>
                        ${module.name || 'Unknown Module'}
                    </h6>
                    <p class="card-text text-muted small">
                        ${module.description || 'No description available'}
                    </p>
                    <div class="mt-auto">
                        <small class="text-muted">
                            Version: ${module.version || 'Unknown'}
                        </small>
                    </div>
                </div>
                <div class="card-footer">
                    <button class="btn btn-sm btn-outline-primary test-module" data-module="${module.name}">
                        <i class="fas fa-play me-1"></i> Test Module
                    </button>
                </div>
            </div>
        `;

        // Add event listener to test button
        const testButton = moduleCard.querySelector('.test-module');
        testButton.addEventListener('click', () => {
            testModule(module.name);
        });

        modulesContainer.appendChild(moduleCard);
    });
}

/**
 * Populate the module select dropdown in enrichment tab
 */
function populateModuleSelect() {
    const moduleSelect = document.getElementById('module-select');

    if (!moduleSelect) {
        return;
    }

    // Clear existing options
    moduleSelect.innerHTML = '<option value="">Select a module...</option>';

    // Add module options
    modules.forEach(module => {
        const option = document.createElement('option');
        option.value = module.name;
        option.textContent = `${module.name} - ${module.description || 'No description'}`;
        moduleSelect.appendChild(option);
    });
}

/**
 * Test a module (placeholder functionality)
 * @param {string} moduleName - Name of the module to test
 */
function testModule(moduleName) {
    // This is a placeholder - in a real implementation, you might want to
    // run a test enrichment or validate the module configuration
    alert(`Testing module: ${moduleName}\n\nThis is a placeholder for module testing functionality.`);
}
