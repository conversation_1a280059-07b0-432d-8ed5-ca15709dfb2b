// Socket instance
let socket = null;

// Initialize socket connection
function initializeSocket() {
  // Create socket connection
  socket = io();
  
  // Connection event
  socket.on('connect', () => {
    console.log('Socket connected:', socket.id);
    showNotification('Connected to real-time updates');
  });
  
  // Disconnection event
  socket.on('disconnect', () => {
    console.log('Socket disconnected');
  });
  
  // Welcome message
  socket.on('welcome', (data) => {
    console.log('Welcome message:', data);
  });
  
  // Business update event
  socket.on('business:update', (data) => {
    console.log('Business updated:', data);
    showNotification(`Business "${data.business.name}" was updated`);
    
    // Update business in list if visible
    updateBusinessInList(data.id, data.business);
    
    // Update business details if currently viewing
    updateBusinessDetails(data.id, data.business);
  });
  
  // Business delete event
  socket.on('business:delete', (data) => {
    console.log('Business deleted:', data);
    showNotification(`Business was deleted`);
    
    // Remove business from list if visible
    removeBusinessFromList(data.id);
    
    // Go back to list if currently viewing deleted business
    const currentBusinessId = document.getElementById('edit-business-id').value;
    if (currentBusinessId === data.id) {
      showBusinessList();
    }
  });
  
  // Note add event
  socket.on('note:add', (data) => {
    console.log('Note added:', data);
    
    // Add note to list if viewing the related business
    const currentBusinessId = document.getElementById('edit-business-id').value;
    if (currentBusinessId === data.businessId) {
      addNoteToList(data.note);
      showNotification('New note added');
    }
  });
  
  // Note update event
  socket.on('note:update', (data) => {
    console.log('Note updated:', data);
    
    // Update note in list if visible
    updateNoteInList(data.id, data.note);
  });
  
  // Note delete event
  socket.on('note:delete', (data) => {
    console.log('Note deleted:', data);
    
    // Remove note from list if visible
    removeNoteFromList(data.id);
  });
  
  // Error event
  socket.on('error', (error) => {
    console.error('Socket error:', error);
    showNotification('Connection error', true);
  });
  
  return socket;
}

// Join a business room for real-time updates
function joinBusinessRoom(businessId) {
  if (socket) {
    socket.emit('join:business', businessId);
  }
}

// Leave a business room
function leaveBusinessRoom(businessId) {
  if (socket) {
    socket.emit('leave:business', businessId);
  }
}

// Disconnect socket
function disconnectSocket() {
  if (socket) {
    socket.disconnect();
    socket = null;
    window.socket = null;
  }
}

// Attach to window for global access
window.initializeSocket = initializeSocket;
window.socket = socket;
