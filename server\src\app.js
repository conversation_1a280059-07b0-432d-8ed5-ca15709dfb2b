const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const { errorHandler } = require('./utils/errorHandler');
const createSearchRoutes = require('./routes/searchRoutes');
const RedisService = require('./services/redisService');
const PlacesService = require('./services/placesService');
require('dotenv').config();

// Initialize Express app
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || 'http://localhost:3000',
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// Initialize services
const initializeApp = async () => {
  try {
    // Initialize Redis service
    const redisService = new RedisService();
    await redisService.initialize();
    
    // Initialize Places service with Redis and Socket.IO
    const placesService = new PlacesService(redisService, io);
    
    // API routes
    app.use('/api/search', createSearchRoutes(placesService));
    
    // Health check endpoint
    app.get('/health', (req, res) => {
      res.status(200).json({ status: 'ok' });
    });
    
    // Error handling middleware
    app.use(errorHandler);
    
    // Socket.IO connection handling
    io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);
      
      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
      });
    });
    
    return { app, server };
  } catch (error) {
    console.error('Failed to initialize app:', error);
    process.exit(1);
  }
};

module.exports = { initializeApp };
