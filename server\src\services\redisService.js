const { createRedisClient } = require('../config/redis');

class RedisService {
  constructor() {
    this.client = null;
    this.initialized = false;
  }

  async initialize() {
    if (!this.initialized) {
      this.client = await createRedisClient();
      this.initialized = true;
    }
    return this;
  }

  // Save a business to Redis
  async saveBusiness(business) {
    if (!business.places_id) {
      throw new Error('Business must have a places_id');
    }

    const key = `business:${business.places_id}`;
    const now = Date.now().toString(); // Convert to string

    // Check if business already exists
    const exists = await this.client.exists(key);
    
    // Prepare business data with stringified values
    const businessData = {
      places_id: business.places_id,
      name: business.name || '',
      address: business.formatted_address || business.address || '',
      phone: business.formatted_phone_number || business.phone || '',
      website: business.website || '',
      rating: business.rating ? business.rating.toString() : '',
      user_ratings_total: business.user_ratings_total ? business.user_ratings_total.toString() : '0',
      keyword: business.keyword || '',
      country: business.country || '',
      city: business.city || '',
      locality: business.locality || '',
      created_at: !exists ? now : (await this.client.hGet(key, 'created_at') || now),
      updated_at: now
    };

    // Save business as hash with stringified values
    await this.client.hSet(key, businessData);

    // Add to indexes
    if (businessData.keyword) {
      await this.client.sAdd(`index:keyword:${businessData.keyword.toLowerCase()}`, business.places_id);
    }
    if (businessData.country) {
      await this.client.sAdd(`index:country:${businessData.country.toLowerCase()}`, business.places_id);
    }
    if (businessData.city) {
      await this.client.sAdd(`index:city:${businessData.city.toLowerCase()}`, business.places_id);
    }
    if (businessData.locality) {
      await this.client.sAdd(`index:locality:${businessData.locality.toLowerCase()}`, business.places_id);
    }
    if (businessData.rating) {
      await this.client.sAdd(`index:rating:${Math.floor(parseFloat(businessData.rating))}`, business.places_id);
    }
    if (businessData.website) {
      await this.client.sAdd('index:has_website', business.places_id);
    }
    if (businessData.phone) {
      await this.client.sAdd('index:has_phone', business.places_id);
    }

    return businessData;
  }

  // Get a business by places_id
  async getBusiness(placesId) {
    const key = `business:${placesId}`;
    const business = await this.client.hGetAll(key);
    
    if (Object.keys(business).length === 0) {
      return null;
    }
    
    return business;
  }

  // Save search metadata
  async saveSearch(searchId, criteria) {
    const key = `search:${searchId}`;
    const search = {
      id: searchId.toString(),
      criteria: JSON.stringify(criteria),
      status: 'in_progress',
      progress: '0',
      totalResults: '0',
      createdAt: Date.now().toString()
    };

    await this.client.hSet(key, search);
    return search;
  }

  // Update search progress
  async updateSearchProgress(searchId, progress, totalResults) {
    const key = `search:${searchId}`;
    
    await this.client.hSet(key, {
      progress: progress.toString(),
      totalResults: totalResults.toString()
    });
  }

  // Complete a search
  async completeSearch(searchId, totalResults) {
    const key = `search:${searchId}`;
    
    await this.client.hSet(key, {
      status: 'completed',
      progress: 100,
      totalResults,
      completedAt: Date.now()
    });
  }

  // Add a result to a search
  async addSearchResult(searchId, placesId) {
    const key = `search:${searchId}:results`;
    await this.client.sAdd(key, placesId.toString());
  }

  // Get search results
  async getSearchResults(searchId) {
    const key = `search:${searchId}:results`;
    const placesIds = await this.client.sMembers(key);
    
    const results = [];
    for (const placesId of placesIds) {
      const business = await this.getBusiness(placesId);
      if (business) {
        // Convert numeric strings back to numbers for the API response
        if (business.rating) business.rating = parseFloat(business.rating);
        if (business.user_ratings_total) business.user_ratings_total = parseInt(business.user_ratings_total);
        if (business.created_at) business.created_at = parseInt(business.created_at);
        if (business.updated_at) business.updated_at = parseInt(business.updated_at);
        results.push(business);
      }
    }
    
    return results;
  }

  // Get search metadata
  async getSearch(searchId) {
    const key = `search:${searchId}`;
    const search = await this.client.hGetAll(key);
    
    if (!search || Object.keys(search).length === 0) {
      return null;
    }
    
    // Parse stored JSON criteria
    if (search.criteria) {
      search.criteria = JSON.parse(search.criteria);
    }
    
    // Convert numeric strings back to numbers
    if (search.progress) search.progress = parseInt(search.progress);
    if (search.totalResults) search.totalResults = parseInt(search.totalResults);
    if (search.createdAt) search.createdAt = parseInt(search.createdAt);
    
    return search;
  }

  // Filter businesses by criteria
  async filterBusinesses(criteria) {
    const { keyword, country, city, locality, minRating, hasWebsite, noWebsite } = criteria;
    let placesIds = new Set();
    let isFirstFilter = true;

    // Filter by keyword
    if (keyword) {
      const keywordIds = await this.client.sMembers(`index:keyword:${keyword.toLowerCase()}`);
      if (isFirstFilter) {
        placesIds = new Set(keywordIds);
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => keywordIds.includes(id)));
      }
    }

    // Filter by country
    if (country) {
      const countryIds = await this.client.sMembers(`index:country:${country.toLowerCase()}`);
      if (isFirstFilter) {
        placesIds = new Set(countryIds);
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => countryIds.includes(id)));
      }
    }

    // Filter by city
    if (city) {
      const cityIds = await this.client.sMembers(`index:city:${city.toLowerCase()}`);
      if (isFirstFilter) {
        placesIds = new Set(cityIds);
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => cityIds.includes(id)));
      }
    }

    // Filter by locality
    if (locality) {
      const localityIds = await this.client.sMembers(`index:locality:${locality.toLowerCase()}`);
      if (isFirstFilter) {
        placesIds = new Set(localityIds);
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => localityIds.includes(id)));
      }
    }

    // Filter by minimum rating
    if (minRating !== undefined) {
      let ratingIds = new Set();
      for (let i = Math.floor(minRating); i <= 5; i++) {
        const ids = await this.client.sMembers(`index:rating:${i}`);
        ids.forEach(id => ratingIds.add(id));
      }
      
      if (isFirstFilter) {
        placesIds = ratingIds;
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => ratingIds.has(id)));
      }
    }

    // Filter by website availability
    if (hasWebsite) {
      const websiteIds = await this.client.sMembers('index:has_website');
      if (isFirstFilter) {
        placesIds = new Set(websiteIds);
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => websiteIds.includes(id)));
      }
    }

    // Filter by noWebsite (opposite of hasWebsite)
    if (noWebsite) {
      const websiteIds = await this.client.sMembers('index:has_website');
      if (isFirstFilter) {
        // All businesses minus those with a website
        const allKeys = await this.client.keys('business:*');
        const allIds = allKeys.map(k => k.replace('business:', ''));
        placesIds = new Set(allIds.filter(id => !websiteIds.includes(id)));
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => !websiteIds.includes(id)));
      }
    }

    // Get business details for filtered IDs
    const results = [];
    for (const placesId of placesIds) {
      const business = await this.getBusiness(placesId);
      if (business) {
        results.push(business);
      }
    }

    return results;
  }
}

module.exports = RedisService;
