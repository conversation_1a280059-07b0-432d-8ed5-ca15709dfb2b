/* Main styles for Business Connector UI */

/* General styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Card styling */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    border: none;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
}

/* Table styling */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
}

/* Pagination styling */
.pagination {
    margin-bottom: 0;
}

/* Log container */
.log-container {
    max-height: 500px;
    overflow-y: auto;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
}

.log-entry {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 0.25rem;
    border-left: 4px solid #6c757d;
}

.log-entry.info {
    border-left-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.log-entry.error {
    border-left-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.05);
}

.log-entry.enrichment {
    border-left-color: #198754;
    background-color: rgba(25, 135, 84, 0.05);
}

.log-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Module cards */
.module-card {
    height: 100%;
    transition: transform 0.2s;
}

.module-card:hover {
    transform: translateY(-5px);
}

.module-card .card-header {
    font-weight: 600;
}

.module-status {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* Business details */
.business-property {
    margin-bottom: 1rem;
}

.property-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

/* Enrichment data */
.enrichment-property {
    margin-bottom: 1rem;
}

.enrichment-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

/* Search input - original can be removed or modified if not used elsewhere */
/* #search-input {
    width: 250px;
} */

/* Styles for sortable table headers */
.table th.sortable {
    cursor: pointer;
}

.table th.sortable .fa-sort,
.table th.sortable .fa-sort-up,
.table th.sortable .fa-sort-down {
    margin-left: 5px;
    color: #adb5bd; /* Lighter color for default sort icon */
}

.table th.sortable.sorted-asc .fa-sort-up,
.table th.sortable.sorted-desc .fa-sort-down {
    color: #007bff; /* Active sort icon color */
}

.table th.sortable.sorted-asc .fa-sort,
.table th.sortable.sorted-desc .fa-sort {
    display: none; /* Hide default sort icon when sorted */
}

/* Expandable Search/Filter Section */
#search-filter-container .card-body {
    background-color: #f8f9fa; /* Light background for the filter area */
}

.pagination-controls {
    display: flex;
    align-items: center;
}

/* Links icons styling */
.links-icons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.icon-link {
    color: #2980b9;
    text-decoration: none;
    font-size: 16px;
    transition: color 0.2s;
}

.icon-link:hover {
    color: #1a5a85;
}

.icon-link.disabled {
    color: #ccc;
    cursor: not-allowed;
}

.icon-link.disabled:hover {
    color: #ccc;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* #search-input {  -- This was the original search input, can be removed
        width: 100%;
        margin-top: 1rem;
    } */

    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header h5 {
        margin-bottom: 1rem;
    }
}
