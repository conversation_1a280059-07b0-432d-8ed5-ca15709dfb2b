const { logger } = require('../services/logService');

/**
 * Get environment variable
 * @param {string} key - Environment variable key
 * @param {string} defaultValue - Default value if not found
 * @returns {string} Environment variable value
 */
function getEnv(key, defaultValue = '') {
  const value = process.env[key];
  
  if (!value && defaultValue === '') {
    logger.warn(`Environment variable ${key} not found and no default provided`);
  }
  
  return value || defaultValue;
}

/**
 * Validate required environment variables
 * @param {Array<string>} requiredVars - List of required environment variables
 * @returns {boolean} True if all required variables are present
 */
function validateEnv(requiredVars) {
  let valid = true;
  
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      logger.error(`Required environment variable ${varName} is missing`);
      valid = false;
    }
  }
  
  return valid;
}

module.exports = {
  getEnv,
  validateEnv
};
