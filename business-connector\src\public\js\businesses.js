// Business listing and management
let currentPage = 1;
let currentLimit = 10;
let currentSearch = '';
let businesses = [];
let totalPages = 0;
let currentSortField = 'id';
let currentSortDirection = 'asc';
let currentWebsite = '';
let currentEnrichmentStatus = '';

/**
 * Initialize businesses functionality
 */
function initBusinesses() {
    // Defensive: Only set up listeners if elements exist
    if (document.getElementById('search-name')) {
        document.getElementById('search-name').addEventListener('input', (e) => {
            currentSearch = e.target.value;
        });
    }
    if (document.getElementById('search-website')) {
        document.getElementById('search-website').addEventListener('input', (e) => {
            currentWebsite = e.target.value;
        });
    }
    if (document.getElementById('filter-enrichment-status')) {
        document.getElementById('filter-enrichment-status').addEventListener('change', (e) => {
            currentEnrichmentStatus = e.target.value;
        });
    }
    if (document.getElementById('apply-filters-btn')) {
        document.getElementById('apply-filters-btn').addEventListener('click', () => {
            currentPage = 1;
            loadBusinesses();
        });
    }
    if (document.getElementById('clear-filters-btn')) {
        document.getElementById('clear-filters-btn').addEventListener('click', () => {
            if (document.getElementById('search-name')) document.getElementById('search-name').value = '';
            if (document.getElementById('search-website')) document.getElementById('search-website').value = '';
            if (document.getElementById('filter-enrichment-status')) document.getElementById('filter-enrichment-status').value = '';
            currentSearch = '';
            currentWebsite = '';
            currentEnrichmentStatus = '';
            currentPage = 1;
            loadBusinesses();
        });
    }
    // Set up sorting
    document.querySelectorAll('.sortable').forEach(th => {
        th.addEventListener('click', () => {
            const sortField = th.getAttribute('data-sort');
            if (currentSortField === sortField) {
                currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                currentSortField = sortField;
                currentSortDirection = 'asc';
            }
            loadBusinesses();
        });
    });
    // Initial load
    loadBusinesses();
}

/**
 * Load businesses with pagination and search
 */
async function loadBusinesses() {
    try {
        // Show loading state
        const tableBody = document.getElementById('businesses-table-body');
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center">Loading...</td></tr>';

        // Build query params with server-side sorting
        let url = `/api/businesses?page=${currentPage}&limit=${currentLimit}`;
        if (currentSearch) url += `&name=${encodeURIComponent(currentSearch)}`;
        if (currentWebsite) url += `&website=${encodeURIComponent(currentWebsite)}`;
        if (currentEnrichmentStatus) url += `&enrichment_status=${encodeURIComponent(currentEnrichmentStatus)}`;

        // Add sorting parameters for server-side sorting
        if (currentSortField) {
            url += `&sort_field=${encodeURIComponent(currentSortField)}`;
            url += `&sort_direction=${encodeURIComponent(currentSortDirection)}`;
        }

        // Fetch businesses
        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP error ${response.status}`);
        const data = await response.json();
        businesses = data.data.businesses;

        // No need for client-side sorting anymore - it's done server-side

        // Update pagination info
        updatePagination(data.data.pagination);

        // Update sort indicators
        updateSortIndicators();

        // Render businesses
        renderBusinesses();
    } catch (error) {
        console.error('Error loading businesses:', error);
        const tableBody = document.getElementById('businesses-table-body');
        tableBody.innerHTML = `<tr><td colspan="9" class="text-center text-danger">Error loading businesses: ${error.message}</td></tr>`;
    }
}

/**
 * Generate enrichment status icons for a business
 * @param {Object} business - Business object
 * @returns {string} HTML string with module icons
 */
function generateEnrichmentStatusIcons(business) {
    const moduleIcons = {
        'companies-house': {
            icon: 'fas fa-building',
            color: '#28a745',
            title: 'Companies House UK'
        },
        'hunter': {
            icon: 'fas fa-envelope',
            color: '#17a2b8',
            title: 'Hunter Email Finder'
        }
    };

    let iconsHtml = '';

    if (business.enrichment_data && Object.keys(business.enrichment_data).length > 0) {
        const enrichmentModules = Object.keys(business.enrichment_data);

        enrichmentModules.forEach(moduleName => {
            const moduleData = business.enrichment_data[moduleName];
            const moduleConfig = moduleIcons[moduleName];

            if (moduleConfig) {
                // Determine status based on enrichment data
                let statusIcon = 'fas fa-check-circle';
                let statusColor = moduleConfig.color;
                let statusTitle = `${moduleConfig.title} - Completed`;

                // Check for specific status indicators
                if (moduleData.enrichment_status === 'low_confidence_match') {
                    statusIcon = 'fas fa-exclamation-triangle';
                    statusColor = '#ffc107';
                    statusTitle = `${moduleConfig.title} - Low Confidence Match`;
                } else if (moduleData.enrichment_status === 'no_match') {
                    statusIcon = 'fas fa-times-circle';
                    statusColor = '#dc3545';
                    statusTitle = `${moduleConfig.title} - No Match Found`;
                } else if (moduleData.error) {
                    statusIcon = 'fas fa-exclamation-circle';
                    statusColor = '#dc3545';
                    statusTitle = `${moduleConfig.title} - Error`;
                }

                iconsHtml += `
                    <span class="enrichment-module-icon me-1" title="${statusTitle}">
                        <i class="${moduleConfig.icon}" style="color: ${moduleConfig.color}; font-size: 14px;"></i>
                        <i class="${statusIcon}" style="color: ${statusColor}; font-size: 10px; margin-left: -4px; margin-top: -8px; position: relative;"></i>
                    </span>
                `;
            }
        });
    }

    // If no enrichment data, show default state
    if (!iconsHtml) {
        iconsHtml = '<span class="text-muted" style="font-size: 12px;">No enrichment</span>';
    }

    return iconsHtml;
}

/**
 * Generate match confidence icon for a business
 * @param {Object} business - Business object
 * @returns {string} HTML string with confidence icon
 */
function generateMatchConfidenceIcon(business) {
    // Check if business has enrichment data with match confidence
    let highestConfidence = 0;

    if (business.enrichment_data && Object.keys(business.enrichment_data).length > 0) {
        Object.keys(business.enrichment_data).forEach(moduleName => {
            const moduleData = business.enrichment_data[moduleName];
            if (moduleData.match_confidence !== undefined) {
                if (moduleData.match_confidence > highestConfidence) {
                    highestConfidence = moduleData.match_confidence;
                }
            }
        });
    }

    // If no confidence data available
    if (highestConfidence === 0) {
        return '<span class="text-muted" style="font-size: 12px;">N/A</span>';
    }

    // Determine icon and color based on confidence level
    let icon, color, bgColor, title;
    const confidencePercent = Math.round(highestConfidence * 100);

    if (highestConfidence >= 0.8) {
        // High confidence - Green
        icon = 'fas fa-check-circle';
        color = '#ffffff';
        bgColor = '#28a745';
        title = `High Confidence (${confidencePercent}%)`;
    } else if (highestConfidence >= 0.5) {
        // Medium confidence - Amber
        icon = 'fas fa-exclamation-triangle';
        color = '#ffffff';
        bgColor = '#ffc107';
        title = `Medium Confidence (${confidencePercent}%)`;
    } else {
        // Low confidence - Red
        icon = 'fas fa-times-circle';
        color = '#ffffff';
        bgColor = '#dc3545';
        title = `Low Confidence (${confidencePercent}%)`;
    }

    return `
        <span class="match-confidence-icon" title="${title}" data-confidence="${highestConfidence}">
            <i class="${icon}" style="color: ${color}; background-color: ${bgColor}; border-radius: 50%; padding: 4px; font-size: 12px;"></i>
            <span style="font-size: 11px; margin-left: 4px;">${confidencePercent}%</span>
        </span>
    `;
}

/**
 * Render businesses in the table
 */
function renderBusinesses() {
    const tableBody = document.getElementById('businesses-table-body');

    if (businesses.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center">No businesses found</td></tr>';
        return;
    }

    tableBody.innerHTML = '';

    businesses.forEach(business => {
        const row = document.createElement('tr');

        // Generate enrichment status icons
        const enrichmentStatusHtml = generateEnrichmentStatusIcons(business);

        // Generate match confidence icon
        const matchConfidenceHtml = generateMatchConfidenceIcon(business);

        row.innerHTML = `
            <td>${business.name || 'N/A'}</td>
            <td>${business.address || 'N/A'}</td>
            <td>${business.touched ? new Date(business.touched).toLocaleString() : 'N/A'}</td>
            <td>
                <span class="links-icons">
                    ${business.website ?
                        `<a href="${business.website}" target="_blank" class="icon-link" title="Visit Website"><i class="fas fa-globe"></i></a>` :
                        `<span class="icon-link disabled" title="No Website"><i class="fas fa-globe"></i></span>`}
                    ${business.phone ?
                        `<a href="tel:${business.phone}" class="icon-link" title="Call ${business.phone}"><i class="fas fa-phone"></i></a>` :
                        `<span class="icon-link disabled" title="No Phone"><i class="fas fa-phone"></i></span>`}
                    <a href="https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(business.address || business.name)}&query_place_id=${encodeURIComponent(business.id)}" target="_blank" class="icon-link" title="View on Google Maps"><i class="fas fa-map-marker-alt"></i></a>
                </span>
            </td>
            <td>${business.rating ? `${business.rating} / 5` : 'N/A'}</td>
            <td>${business.user_ratings_total || '0'}</td>
            <td>${enrichmentStatusHtml}</td>
            <td>${matchConfidenceHtml}</td>
            <td>
                <button class="btn btn-sm btn-primary view-business" data-id="${business.id}">
                    <i class="fas fa-eye me-1"></i> View
                </button>
            </td>
        `;

        // Add event listener to view button
        const viewButton = row.querySelector('.view-business');
        viewButton.addEventListener('click', () => {
            openBusinessDetail(business.id);
        });

        tableBody.appendChild(row);
    });
}

/**
 * Sort businesses array
 * @param {Array} businesses - Array of business objects
 * @param {string} field - Field to sort by
 * @param {string} direction - Sort direction ('asc' or 'desc')
 * @returns {Array} Sorted businesses array
 */
function sortBusinesses(businesses, field, direction) {
    return businesses.slice().sort((a, b) => {
        let valA = a[field] || '';
        let valB = b[field] || '';

        if (field === 'rating' || field === 'user_ratings_total') {
            valA = parseFloat(valA) || 0;
            valB = parseFloat(valB) || 0;
        } else if (field === 'touched') {
            valA = valA ? new Date(valA).getTime() : 0;
            valB = valB ? new Date(valB).getTime() : 0;
        } else if (field === 'match_confidence') {
            // Extract highest match confidence from enrichment data
            valA = getHighestMatchConfidence(a);
            valB = getHighestMatchConfidence(b);
        } else {
            valA = valA.toString().toLowerCase();
            valB = valB.toString().toLowerCase();
        }

        if (valA < valB) return direction === 'asc' ? -1 : 1;
        if (valA > valB) return direction === 'asc' ? 1 : -1;
        return 0;
    });
}

/**
 * Get the highest match confidence from a business's enrichment data
 * @param {Object} business - Business object
 * @returns {number} Highest match confidence (0-1)
 */
function getHighestMatchConfidence(business) {
    let highestConfidence = 0;

    if (business.enrichment_data && Object.keys(business.enrichment_data).length > 0) {
        Object.keys(business.enrichment_data).forEach(moduleName => {
            const moduleData = business.enrichment_data[moduleName];
            if (moduleData.match_confidence !== undefined) {
                if (moduleData.match_confidence > highestConfidence) {
                    highestConfidence = moduleData.match_confidence;
                }
            }
        });
    }

    return highestConfidence;
}

/**
 * Update sort indicators on table headers
 */
function updateSortIndicators() {
    // Remove all existing sort classes
    document.querySelectorAll('.sortable').forEach(th => {
        th.classList.remove('sorted-asc', 'sorted-desc');
        const icon = th.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-sort';
        }
    });

    // Add sort class to current sorted column
    if (currentSortField) {
        const currentHeader = document.querySelector(`[data-sort="${currentSortField}"]`);
        if (currentHeader) {
            currentHeader.classList.add(currentSortDirection === 'asc' ? 'sorted-asc' : 'sorted-desc');
            const icon = currentHeader.querySelector('i');
            if (icon) {
                icon.className = currentSortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            }
        }
    }
}

/**
 * Update pagination controls
 * @param {Object} pagination - Pagination info
 */
function updatePagination(pagination) {
    const paginationInfo = document.getElementById('pagination-info');
    const paginationElement = document.getElementById('pagination');

    // Update total pages
    totalPages = pagination.pages;

    // Update pagination info text
    const start = (pagination.page - 1) * pagination.limit + 1;
    const end = Math.min(pagination.page * pagination.limit, pagination.total);
    paginationInfo.textContent = `Showing ${start}-${end} of ${pagination.total} businesses`;

    // Generate pagination controls
    paginationElement.innerHTML = '';

    // Previous button
    const prevItem = document.createElement('li');
    prevItem.className = `page-item ${pagination.page === 1 ? 'disabled' : ''}`;
    prevItem.innerHTML = '<a class="page-link" href="#"><i class="fas fa-chevron-left"></i></a>';
    prevItem.addEventListener('click', (e) => {
        e.preventDefault();
        if (pagination.page > 1) {
            currentPage = pagination.page - 1;
            loadBusinesses();
        }
    });
    paginationElement.appendChild(prevItem);

    // Page numbers
    const maxPages = 5;
    const startPage = Math.max(1, pagination.page - Math.floor(maxPages / 2));
    const endPage = Math.min(pagination.pages, startPage + maxPages - 1);

    for (let i = startPage; i <= endPage; i++) {
        const pageItem = document.createElement('li');
        pageItem.className = `page-item ${i === pagination.page ? 'active' : ''}`;
        pageItem.innerHTML = `<a class="page-link" href="#">${i}</a>`;
        pageItem.addEventListener('click', (e) => {
            e.preventDefault();
            currentPage = i;
            loadBusinesses();
        });
        paginationElement.appendChild(pageItem);
    }

    // Next button
    const nextItem = document.createElement('li');
    nextItem.className = `page-item ${pagination.page === pagination.pages ? 'disabled' : ''}`;
    nextItem.innerHTML = '<a class="page-link" href="#"><i class="fas fa-chevron-right"></i></a>';
    nextItem.addEventListener('click', (e) => {
        e.preventDefault();
        if (pagination.page < pagination.pages) {
            currentPage = pagination.page + 1;
            loadBusinesses();
        }
    });
    paginationElement.appendChild(nextItem);
}

/**
 * Open business detail modal
 * @param {string} businessId - Business ID
 */
async function openBusinessDetail(businessId) {
    try {
        // Show loading state
        const detailsContainer = document.getElementById('business-details-container');
        detailsContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';

        // Set modal title (fix: use correct ID)
        const modalTitle = document.getElementById('business-detail-modal-title');
        if (modalTitle) {
            modalTitle.textContent = `Business Details (ID: ${businessId})`;
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('business-detail-modal'));
        modal.show();

        // Fetch business details
        const data = await fetchBusiness(businessId);
        const business = data.business;
        const enrichment = data.enrichment;

        // Render business details
        renderBusinessDetails(business);

        // Set up enrichment tab
        if (typeof setupEnrichmentTab === 'function') {
            setupEnrichmentTab(businessId, business, enrichment);
        }
    } catch (error) {
        console.error(`Error opening business detail for ${businessId}:`, error);
        const detailsContainer = document.getElementById('business-details-container');
        if (detailsContainer) {
            detailsContainer.innerHTML = `<div class="alert alert-danger">Error loading business details: ${error.message}</div>`;
        }
    }
}

/**
 * Render business details
 * @param {Object} business - Business object
 */
function renderBusinessDetails(business) {
    const detailsContainer = document.getElementById('business-details-container');

    // Create details HTML
    let html = '';

    // Core business properties
    const coreProperties = ['name', 'address', 'city', 'state', 'country', 'phone', 'website', 'category'];

    coreProperties.forEach(prop => {
        if (business[prop]) {
            html += `
                <div class="business-property">
                    <div class="property-label">${prop.charAt(0).toUpperCase() + prop.slice(1)}</div>
                    <div class="property-value">${business[prop]}</div>
                </div>
            `;
        }
    });

    // Additional properties
    const additionalProps = Object.keys(business).filter(key =>
        !coreProperties.includes(key) && key !== 'id' && key !== 'enrichmentStatus' && key !== 'enrichmentModules'
    );

    if (additionalProps.length > 0) {
        html += '<hr>';
        html += '<h6>Additional Information</h6>';

        additionalProps.forEach(prop => {
            html += `
                <div class="business-property">
                    <div class="property-label">${prop.charAt(0).toUpperCase() + prop.slice(1)}</div>
                    <div class="property-value">${business[prop]}</div>
                </div>
            `;
        });
    }

    detailsContainer.innerHTML = html;
}
