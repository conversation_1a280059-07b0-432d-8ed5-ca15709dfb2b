// Business listing and management
let currentPage = 1;
let currentLimit = 10;
let currentSearch = '';
let businesses = [];
let totalPages = 0;
let currentSortField = 'id';
let currentSortDirection = 'asc';
let currentWebsite = '';
let currentEnrichmentStatus = '';

/**
 * Initialize businesses functionality
 */
function initBusinesses() {
    // Defensive: Only set up listeners if elements exist
    if (document.getElementById('search-name')) {
        document.getElementById('search-name').addEventListener('input', (e) => {
            currentSearch = e.target.value;
        });
    }
    if (document.getElementById('search-website')) {
        document.getElementById('search-website').addEventListener('input', (e) => {
            currentWebsite = e.target.value;
        });
    }
    if (document.getElementById('filter-enrichment-status')) {
        document.getElementById('filter-enrichment-status').addEventListener('change', (e) => {
            currentEnrichmentStatus = e.target.value;
        });
    }
    if (document.getElementById('apply-filters-btn')) {
        document.getElementById('apply-filters-btn').addEventListener('click', () => {
            currentPage = 1;
            loadBusinesses();
        });
    }
    if (document.getElementById('clear-filters-btn')) {
        document.getElementById('clear-filters-btn').addEventListener('click', () => {
            if (document.getElementById('search-name')) document.getElementById('search-name').value = '';
            if (document.getElementById('search-website')) document.getElementById('search-website').value = '';
            if (document.getElementById('filter-enrichment-status')) document.getElementById('filter-enrichment-status').value = '';
            currentSearch = '';
            currentWebsite = '';
            currentEnrichmentStatus = '';
            currentPage = 1;
            loadBusinesses();
        });
    }
    // Set up sorting
    document.querySelectorAll('.sortable').forEach(th => {
        th.addEventListener('click', () => {
            const sortField = th.getAttribute('data-sort');
            if (currentSortField === sortField) {
                currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                currentSortField = sortField;
                currentSortDirection = 'asc';
            }
            loadBusinesses();
        });
    });
    // Initial load
    loadBusinesses();
}

/**
 * Load businesses with pagination and search
 */
async function loadBusinesses() {
    try {
        // Show loading state
        const tableBody = document.getElementById('businesses-table-body');
        tableBody.innerHTML = '<tr><td colspan="5" class="text-center">Loading...</td></tr>';

        // Build query params
        let url = `/api/businesses?page=${currentPage}&limit=${currentLimit}`;
        if (currentSearch) url += `&name=${encodeURIComponent(currentSearch)}`;
        if (currentWebsite) url += `&website=${encodeURIComponent(currentWebsite)}`;
        if (currentEnrichmentStatus) url += `&enrichment_status=${encodeURIComponent(currentEnrichmentStatus)}`;
        if (currentSortField) url += `&sort=${encodeURIComponent(currentSortField)}`;
        if (currentSortDirection) url += `&direction=${encodeURIComponent(currentSortDirection)}`;

        // Fetch businesses
        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP error ${response.status}`);
        const data = await response.json();
        businesses = data.data.businesses;

        // Update pagination info
        updatePagination(data.data.pagination);

        // Render businesses
        renderBusinesses();
    } catch (error) {
        console.error('Error loading businesses:', error);
        const tableBody = document.getElementById('businesses-table-body');
        tableBody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">Error loading businesses: ${error.message}</td></tr>`;
    }
}

/**
 * Render businesses in the table
 */
function renderBusinesses() {
    const tableBody = document.getElementById('businesses-table-body');

    if (businesses.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No businesses found</td></tr>';
        return;
    }

    tableBody.innerHTML = '';

    businesses.forEach(business => {
        const row = document.createElement('tr');

        // Determine enrichment status
        let enrichmentStatus = 'None';
        let enrichmentBadgeClass = 'bg-secondary';

        if (business.enrichment_data && Object.keys(business.enrichment_data).length > 0) {
            enrichmentStatus = 'Enriched';
            enrichmentBadgeClass = 'bg-success';
        } else if (business.enrichment_status === 'pending') {
            enrichmentStatus = 'Pending';
            enrichmentBadgeClass = 'bg-warning';
        } else if (business.enrichment_status === 'failed') {
            enrichmentStatus = 'Failed';
            enrichmentBadgeClass = 'bg-danger';
        }

        row.innerHTML = `
            <td>${business.id || 'N/A'}</td>
            <td>${business.name || 'N/A'}</td>
            <td>
                ${business.website ?
                    `<a href="${business.website}" target="_blank" title="Visit Website">${business.website}</a>` :
                    'N/A'
                }
            </td>
            <td>
                <span class="badge ${enrichmentBadgeClass}">${enrichmentStatus}</span>
            </td>
            <td>
                <button class="btn btn-sm btn-primary view-business" data-id="${business.id}">
                    <i class="fas fa-eye me-1"></i> View
                </button>
            </td>
        `;

        // Add event listener to view button
        const viewButton = row.querySelector('.view-business');
        viewButton.addEventListener('click', () => {
            openBusinessDetail(business.id);
        });

        tableBody.appendChild(row);
    });
}

/**
 * Update pagination controls
 * @param {Object} pagination - Pagination info
 */
function updatePagination(pagination) {
    const paginationInfo = document.getElementById('pagination-info');
    const paginationElement = document.getElementById('pagination');

    // Update total pages
    totalPages = pagination.pages;

    // Update pagination info text
    const start = (pagination.page - 1) * pagination.limit + 1;
    const end = Math.min(pagination.page * pagination.limit, pagination.total);
    paginationInfo.textContent = `Showing ${start}-${end} of ${pagination.total} businesses`;

    // Generate pagination controls
    paginationElement.innerHTML = '';

    // Previous button
    const prevItem = document.createElement('li');
    prevItem.className = `page-item ${pagination.page === 1 ? 'disabled' : ''}`;
    prevItem.innerHTML = '<a class="page-link" href="#"><i class="fas fa-chevron-left"></i></a>';
    prevItem.addEventListener('click', (e) => {
        e.preventDefault();
        if (pagination.page > 1) {
            currentPage = pagination.page - 1;
            loadBusinesses();
        }
    });
    paginationElement.appendChild(prevItem);

    // Page numbers
    const maxPages = 5;
    const startPage = Math.max(1, pagination.page - Math.floor(maxPages / 2));
    const endPage = Math.min(pagination.pages, startPage + maxPages - 1);

    for (let i = startPage; i <= endPage; i++) {
        const pageItem = document.createElement('li');
        pageItem.className = `page-item ${i === pagination.page ? 'active' : ''}`;
        pageItem.innerHTML = `<a class="page-link" href="#">${i}</a>`;
        pageItem.addEventListener('click', (e) => {
            e.preventDefault();
            currentPage = i;
            loadBusinesses();
        });
        paginationElement.appendChild(pageItem);
    }

    // Next button
    const nextItem = document.createElement('li');
    nextItem.className = `page-item ${pagination.page === pagination.pages ? 'disabled' : ''}`;
    nextItem.innerHTML = '<a class="page-link" href="#"><i class="fas fa-chevron-right"></i></a>';
    nextItem.addEventListener('click', (e) => {
        e.preventDefault();
        if (pagination.page < pagination.pages) {
            currentPage = pagination.page + 1;
            loadBusinesses();
        }
    });
    paginationElement.appendChild(nextItem);
}

/**
 * Open business detail modal
 * @param {string} businessId - Business ID
 */
async function openBusinessDetail(businessId) {
    try {
        // Show loading state
        const detailsContainer = document.getElementById('business-details-container');
        detailsContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';

        // Set modal title (fix: use correct ID)
        const modalTitle = document.getElementById('business-detail-modal-title');
        if (modalTitle) {
            modalTitle.textContent = `Business Details (ID: ${businessId})`;
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('business-detail-modal'));
        modal.show();

        // Fetch business details
        const data = await fetchBusiness(businessId);
        const business = data.business;
        const enrichment = data.enrichment;

        // Render business details
        renderBusinessDetails(business);

        // Set up enrichment tab
        if (typeof setupEnrichmentTab === 'function') {
            setupEnrichmentTab(businessId, business, enrichment);
        }
    } catch (error) {
        console.error(`Error opening business detail for ${businessId}:`, error);
        const detailsContainer = document.getElementById('business-details-container');
        if (detailsContainer) {
            detailsContainer.innerHTML = `<div class="alert alert-danger">Error loading business details: ${error.message}</div>`;
        }
    }
}

/**
 * Render business details
 * @param {Object} business - Business object
 */
function renderBusinessDetails(business) {
    const detailsContainer = document.getElementById('business-details-container');

    // Create details HTML
    let html = '';

    // Core business properties
    const coreProperties = ['name', 'address', 'city', 'state', 'country', 'phone', 'website', 'category'];

    coreProperties.forEach(prop => {
        if (business[prop]) {
            html += `
                <div class="business-property">
                    <div class="property-label">${prop.charAt(0).toUpperCase() + prop.slice(1)}</div>
                    <div class="property-value">${business[prop]}</div>
                </div>
            `;
        }
    });

    // Additional properties
    const additionalProps = Object.keys(business).filter(key =>
        !coreProperties.includes(key) && key !== 'id' && key !== 'enrichmentStatus' && key !== 'enrichmentModules'
    );

    if (additionalProps.length > 0) {
        html += '<hr>';
        html += '<h6>Additional Information</h6>';

        additionalProps.forEach(prop => {
            html += `
                <div class="business-property">
                    <div class="property-label">${prop.charAt(0).toUpperCase() + prop.slice(1)}</div>
                    <div class="property-value">${business[prop]}</div>
                </div>
            `;
        });
    }

    detailsContainer.innerHTML = html;
}
