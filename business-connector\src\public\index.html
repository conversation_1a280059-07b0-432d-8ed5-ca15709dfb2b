<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Connector</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-project-diagram me-2"></i>
                Business Connector
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" id="businesses-tab">Businesses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="modules-tab">Modules</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logs-tab">Logs</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Main Content Area -->
        <div id="businesses-content">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" id="section-title">Businesses</h5>
                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#filter-section" aria-expanded="false" aria-controls="filter-section">
                            <i class="fas fa-filter me-1"></i> Filters / Search
                        </button>
                    </div>
                    <div class="collapse mt-3" id="filter-section">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control form-control-sm" id="search-name" placeholder="Search by Name...">
                            </div>
                            <div class="col-md-3">
                                <input type="text" class="form-control form-control-sm" id="search-website" placeholder="Search by Website...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filter-enrichment-status">
                                    <option value="">All Enrichment Statuses</option>
                                    <option value="PENDING">Pending</option>
                                    <option value="COMPLETED">Completed</option>
                                    <option value="FAILED">Failed</option>
                                    <option value="NOT_ENRICHED">Not Enriched</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex justify-content-end">
                                <button class="btn btn-primary btn-sm me-2" id="apply-filters-btn">Apply</button>
                                <button class="btn btn-secondary btn-sm" id="clear-filters-btn">Clear</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="id">ID <i class="fas fa-sort"></i></th>
                                    <th class="sortable" data-sort="name">Name <i class="fas fa-sort"></i></th>
                                    <th class="sortable" data-sort="website">Website <i class="fas fa-sort"></i></th>
                                    <th>Enrichment Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="businesses-table-body">
                                <!-- Business rows will be populated here by businesses.js -->
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span id="pagination-info"></span> <!-- Corrected ID -->
                        </div>
                        <div>
                            <nav>
                                <ul class="pagination" id="pagination"></ul> <!-- Corrected ID and element type -->
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="modules-content" class="d-none">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Modules</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="modules-container">
                        <!-- Module cards will be populated here by modules.js -->
                    </div>
                </div>
            </div>
        </div>

        <div id="logs-content" class="d-none">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Logs</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <select class="form-select" id="log-filter">
                            <option value="all">All Logs</option>
                            <option value="info">Info</option>
                            <option value="error">Error</option>
                            <option value="enrichment">Enrichment</option>
                        </select>
                    </div>
                    <div class="log-container" id="logs-list">
                        <!-- Logs will be populated here by logs.js -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Business Detail Modal -->
    <div class="modal fade" id="business-detail-modal" tabindex="-1" aria-labelledby="businessDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="business-detail-modal-title">Business Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="business-detail-tabs-list" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="details-tab-button" data-bs-toggle="tab" data-bs-target="#details-tab-pane" type="button" role="tab" aria-controls="details-tab-pane" aria-selected="true">Details</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="enrichment-tab-button" data-bs-toggle="tab" data-bs-target="#enrichment-tab-pane" type="button" role="tab" aria-controls="enrichment-tab-pane" aria-selected="false">Enrichment</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="business-detail-tab-content">
                        <div class="tab-pane fade show active" id="details-tab-pane" role="tabpanel" aria-labelledby="details-tab-button" tabindex="0">
                            <div id="business-details-container">
                                <!-- Business details will be populated here by businesses.js -->
                            </div>
                        </div>
                        <div class="tab-pane fade" id="enrichment-tab-pane" role="tabpanel" aria-labelledby="enrichment-tab-button" tabindex="0">
                            <div class="mb-3">
                                <label for="module-select" class="form-label">Select Module for Enrichment</label>
                                <select class="form-select" id="module-select">
                                    <!-- Modules will be populated here by enrichment.js or modules.js -->
                                </select>
                            </div>
                            <div id="enrichment-output-container">
                                <!-- Enrichment data/form will be populated here -->
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-primary" id="run-enrichment-btn">Run Enrichment</button>
                                <button class="btn btn-success d-none" id="save-enrichment-btn">Save Enrichment Data</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/businesses.js"></script>
    <script src="js/modules.js"></script>
    <script src="js/enrichment.js"></script>
    <script src="js/logs.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
