<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Connector</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-project-diagram me-2"></i>
                Business Connector
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" id="businesses-tab">Businesses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="modules-tab">Modules</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logs-tab">Logs</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Main Content Area -->
        <div id="businesses-content">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" id="section-title">Businesses</h5>
                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#filter-section" aria-expanded="false" aria-controls="filter-section">
                            <i class="fas fa-filter me-1"></i> Filters / Search
                        </button>
                    </div>
                    <div class="collapse mt-3" id="filter-section">
                        <!-- Modern 3-Column Filter Layout -->
                        <div class="row g-4">
                            <!-- Column 1: Area & Location Filters -->
                            <div class="col-md-4">
                                <div class="filter-section">
                                    <h6 class="filter-section-title mb-3">
                                        <i class="fas fa-map-marker-alt me-2"></i>Location & Area
                                    </h6>

                                    <!-- Quick Search -->
                                    <div class="mb-3">
                                        <input type="text" class="form-control" id="search-name" placeholder="Search business name...">
                                    </div>

                                    <!-- Location Inputs -->
                                    <div class="mb-3">
                                        <input type="text" class="form-control mb-2" id="search-city" placeholder="City">
                                        <input type="text" class="form-control" id="search-address" placeholder="Address or postcode">
                                    </div>

                                    <!-- Star Rating Range -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Star Rating</label>
                                        <div class="star-rating-filter d-flex flex-wrap gap-1">
                                            <button type="button" class="btn btn-outline-warning btn-sm star-filter active" data-rating="">Any</button>
                                            <button type="button" class="btn btn-outline-warning btn-sm star-filter" data-rating="1">1★</button>
                                            <button type="button" class="btn btn-outline-warning btn-sm star-filter" data-rating="2">2★</button>
                                            <button type="button" class="btn btn-outline-warning btn-sm star-filter" data-rating="3">3★</button>
                                            <button type="button" class="btn btn-outline-warning btn-sm star-filter" data-rating="4">4★</button>
                                            <button type="button" class="btn btn-outline-warning btn-sm star-filter" data-rating="5">5★</button>
                                        </div>
                                        <small class="text-muted">Click to filter by exact rating</small>
                                    </div>

                                    <!-- Review Count Range -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Minimum Reviews</label>
                                        <div class="review-count-filter d-flex flex-wrap gap-1">
                                            <button type="button" class="btn btn-outline-info btn-sm review-filter active" data-count="">Any</button>
                                            <button type="button" class="btn btn-outline-info btn-sm review-filter" data-count="1">1+</button>
                                            <button type="button" class="btn btn-outline-info btn-sm review-filter" data-count="10">10+</button>
                                            <button type="button" class="btn btn-outline-info btn-sm review-filter" data-count="50">50+</button>
                                            <button type="button" class="btn btn-outline-info btn-sm review-filter" data-count="100">100+</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Column 2: Enrichment & Data Quality -->
                            <div class="col-md-4">
                                <div class="filter-section">
                                    <h6 class="filter-section-title mb-3">
                                        <i class="fas fa-database me-2"></i>Data & Enrichment
                                    </h6>

                                    <!-- Contact Info Toggles -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Contact Information</label>
                                        <div class="toggle-group">
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="toggle-has-website">
                                                <label class="form-check-label" for="toggle-has-website">
                                                    <i class="fas fa-globe me-1"></i> Has Website
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="toggle-has-phone">
                                                <label class="form-check-label" for="toggle-has-phone">
                                                    <i class="fas fa-phone me-1"></i> Has Phone Number
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="toggle-has-google-listing">
                                                <label class="form-check-label" for="toggle-has-google-listing">
                                                    <i class="fab fa-google me-1"></i> Has Google Places
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Enrichment Status -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Enrichment Status</label>
                                        <div class="enrichment-filter d-flex flex-wrap gap-1">
                                            <button type="button" class="btn btn-outline-secondary btn-sm enrichment-filter-btn active" data-status="">All</button>
                                            <button type="button" class="btn btn-outline-success btn-sm enrichment-filter-btn" data-status="enriched">
                                                <i class="fas fa-check me-1"></i>Enriched
                                            </button>
                                            <button type="button" class="btn btn-outline-danger btn-sm enrichment-filter-btn" data-status="not_enriched">
                                                <i class="fas fa-times me-1"></i>Not Enriched
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Match Confidence -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Match Confidence</label>
                                        <div class="confidence-filter d-flex flex-wrap gap-1">
                                            <button type="button" class="btn btn-outline-secondary btn-sm confidence-filter-btn active" data-confidence="">Any</button>
                                            <button type="button" class="btn btn-outline-success btn-sm confidence-filter-btn" data-confidence="high">
                                                <i class="fas fa-circle text-success me-1"></i>High
                                            </button>
                                            <button type="button" class="btn btn-outline-warning btn-sm confidence-filter-btn" data-confidence="medium">
                                                <i class="fas fa-circle text-warning me-1"></i>Medium
                                            </button>
                                            <button type="button" class="btn btn-outline-danger btn-sm confidence-filter-btn" data-confidence="low">
                                                <i class="fas fa-circle text-danger me-1"></i>Low
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Enrichment Modules -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Enrichment Modules</label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="toggle-companies-house">
                                            <label class="form-check-label" for="toggle-companies-house">
                                                <i class="fas fa-building me-1"></i> Companies House UK
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Column 3: Date Filters -->
                            <div class="col-md-4">
                                <div class="filter-section">
                                    <h6 class="filter-section-title mb-3">
                                        <i class="fas fa-calendar-alt me-2"></i>Date Filters
                                    </h6>

                                    <!-- Created Date Range -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Created Date</label>
                                        <div class="date-range-group">
                                            <input type="date" class="form-control form-control-sm mb-2" id="filter-created-after" placeholder="From">
                                            <input type="date" class="form-control form-control-sm" id="filter-created-before" placeholder="To">
                                        </div>
                                    </div>

                                    <!-- Last Touched Date Range -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Last Touched</label>
                                        <div class="date-range-group">
                                            <input type="date" class="form-control form-control-sm mb-2" id="filter-touched-after" placeholder="From">
                                            <input type="date" class="form-control form-control-sm" id="filter-touched-before" placeholder="To">
                                        </div>
                                    </div>

                                    <!-- Quick Date Filters -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold mb-2">Quick Filters</label>
                                        <div class="quick-date-filter d-flex flex-wrap gap-1">
                                            <button type="button" class="btn btn-outline-primary btn-sm quick-date-btn" data-period="today">Today</button>
                                            <button type="button" class="btn btn-outline-primary btn-sm quick-date-btn" data-period="week">This Week</button>
                                            <button type="button" class="btn btn-outline-primary btn-sm quick-date-btn" data-period="month">This Month</button>
                                        </div>
                                    </div>

                                    <!-- Active Filters & Actions -->
                                    <div class="mt-4">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <span class="badge bg-primary" id="active-filters-count">0 filters</span>
                                            <button class="btn btn-outline-secondary btn-sm" id="clear-filters-btn">
                                                <i class="fas fa-times me-1"></i>Clear All
                                            </button>
                                        </div>
                                        <button class="btn btn-primary w-100" id="apply-filters-btn">
                                            <i class="fas fa-search me-2"></i>Apply Filters
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="name">Name <i class="fas fa-sort"></i></th>
                                    <th class="sortable" data-sort="address">Address <i class="fas fa-sort"></i></th>
                                    <th class="sortable" data-sort="touched">Touched <i class="fas fa-sort"></i></th>
                                    <th>Links</th>
                                    <th class="sortable" data-sort="rating">Rating <i class="fas fa-sort"></i></th>
                                    <th class="sortable" data-sort="user_ratings_total">Ratings <i class="fas fa-sort"></i></th>
                                    <th>Enrichment Status</th>
                                    <th class="sortable" data-sort="match_confidence">Match Confidence <i class="fas fa-sort"></i></th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="businesses-table-body">
                                <!-- Business rows will be populated here by businesses.js -->
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="d-flex align-items-center">
                            <span id="pagination-info" class="me-3"></span>
                            <div class="d-flex align-items-center">
                                <label for="results-per-page" class="form-label me-2 mb-0">Results per page:</label>
                                <select class="form-select form-select-sm" id="results-per-page" style="width: auto;">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="500">500</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <nav>
                                <ul class="pagination" id="pagination"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="modules-content" class="d-none">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Modules</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="modules-container">
                        <!-- Module cards will be populated here by modules.js -->
                    </div>
                </div>
            </div>
        </div>

        <div id="logs-content" class="d-none">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Logs</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <select class="form-select" id="log-filter">
                            <option value="all">All Logs</option>
                            <option value="info">Info</option>
                            <option value="error">Error</option>
                            <option value="enrichment">Enrichment</option>
                        </select>
                    </div>
                    <div class="log-container" id="logs-list">
                        <!-- Logs will be populated here by logs.js -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Business Detail Modal -->
    <div class="modal fade" id="business-detail-modal" tabindex="-1" aria-labelledby="businessDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="business-detail-modal-title">Business Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="business-detail-tabs-list" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="details-tab-button" data-bs-toggle="tab" data-bs-target="#details-tab-pane" type="button" role="tab" aria-controls="details-tab-pane" aria-selected="true">Details</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="enrichment-tab-button" data-bs-toggle="tab" data-bs-target="#enrichment-tab-pane" type="button" role="tab" aria-controls="enrichment-tab-pane" aria-selected="false">Enrichment</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="business-detail-tab-content">
                        <div class="tab-pane fade show active" id="details-tab-pane" role="tabpanel" aria-labelledby="details-tab-button" tabindex="0">
                            <div id="business-details-container">
                                <!-- Business details will be populated here by businesses.js -->
                            </div>
                        </div>
                        <div class="tab-pane fade" id="enrichment-tab-pane" role="tabpanel" aria-labelledby="enrichment-tab-button" tabindex="0">
                            <div class="mb-3">
                                <label for="module-select" class="form-label">Select Module for Enrichment</label>
                                <select class="form-select" id="module-select">
                                    <!-- Modules will be populated here by enrichment.js or modules.js -->
                                </select>
                            </div>
                            <div id="enrichment-output-container">
                                <!-- Enrichment data/form will be populated here -->
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-primary" id="run-enrichment-btn">Run Enrichment</button>
                                <button class="btn btn-success d-none" id="save-enrichment-btn">Save Enrichment Data</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/businesses.js"></script>
    <script src="js/modules.js"></script>
    <script src="js/enrichment.js"></script>
    <script src="js/logs.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
