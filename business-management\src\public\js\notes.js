// Notes related functions

// DOM elements
const notesList = document.getElementById('notes-list');
const noNotes = document.getElementById('no-notes');
const addNoteBtn = document.getElementById('add-note-btn');
const addNoteFormContainer = document.getElementById('add-note-form-container');
const addNoteForm = document.getElementById('add-note-form');
const cancelAddNoteBtn = document.getElementById('cancel-add-note');
const editNoteModal = document.getElementById('edit-note-modal');
const editNoteForm = document.getElementById('edit-note-form');
const closeEditNoteModalBtn = document.getElementById('close-edit-note-modal');
const deleteNoteBtn = document.getElementById('delete-note-btn');

// Load notes for a business
async function loadBusinessNotes(businessId) {
  try {
    // Clear notes list
    notesList.innerHTML = '';
    noNotes.classList.add('hidden');
    
    // Fetch notes
    const notes = await notesApi.getBusinessNotes(businessId);
    
    // Check if no notes
    if (notes.length === 0) {
      noNotes.classList.remove('hidden');
      return;
    }
    
    // Render notes
    renderNotesList(notes);
  } catch (error) {
    console.error('Error loading notes:', error);
    showNotification('Failed to load notes', true);
  }
}

// Render notes list
function renderNotesList(notes) {
  notesList.innerHTML = '';
  
  notes.forEach(note => {
    addNoteToList(note);
  });
}

// Add note to list
function addNoteToList(note) {
  // Hide no notes message
  noNotes.classList.add('hidden');
  
  const noteElement = document.createElement('div');
  noteElement.className = 'note-item';
  noteElement.setAttribute('data-id', note.id);
  
  noteElement.innerHTML = `
    <div class="note-header">
      <div class="note-meta">
        <strong>${note.createdBy}</strong> - ${formatDate(note.createdAt)}
        ${note.updatedAt !== note.createdAt ? `(Updated: ${formatDate(note.updatedAt)})` : ''}
      </div>
      <div class="note-actions">
        <button class="edit-note" data-id="${note.id}"><i class="fas fa-edit"></i></button>
      </div>
    </div>
    <div class="note-content">${note.content}</div>
  `;
  
  // Add to list (at the beginning for newest first)
  notesList.insertBefore(noteElement, notesList.firstChild);
  
  // Add event listener to edit button
  noteElement.querySelector('.edit-note').addEventListener('click', () => {
    openEditNoteModal(note);
  });
}

// Update note in list
function updateNoteInList(noteId, note) {
  const noteElement = notesList.querySelector(`.note-item[data-id="${noteId}"]`);
  
  if (noteElement) {
    noteElement.innerHTML = `
      <div class="note-header">
        <div class="note-meta">
          <strong>${note.createdBy}</strong> - ${formatDate(note.createdAt)}
          ${note.updatedAt !== note.createdAt ? `(Updated: ${formatDate(note.updatedAt)})` : ''}
        </div>
        <div class="note-actions">
          <button class="edit-note" data-id="${note.id}"><i class="fas fa-edit"></i></button>
        </div>
      </div>
      <div class="note-content">${note.content}</div>
    `;
    
    // Re-add event listener
    noteElement.querySelector('.edit-note').addEventListener('click', () => {
      openEditNoteModal(note);
    });
  }
}

// Remove note from list
function removeNoteFromList(noteId) {
  const noteElement = notesList.querySelector(`.note-item[data-id="${noteId}"]`);
  
  if (noteElement) {
    noteElement.remove();
    
    // Check if list is empty
    if (notesList.children.length === 0) {
      noNotes.classList.remove('hidden');
    }
  }
}

// Show add note form
function showAddNoteForm() {
  addNoteFormContainer.classList.remove('hidden');
  addNoteForm.reset();
  document.getElementById('note-content').focus();
}

// Hide add note form
function hideAddNoteForm() {
  addNoteFormContainer.classList.add('hidden');
}

// Open edit note modal
function openEditNoteModal(note) {
  // Set form values
  document.getElementById('edit-note-id').value = note.id;
  document.getElementById('edit-note-content').value = note.content;
  
  // Show modal
  editNoteModal.classList.remove('hidden');
}

// Close edit note modal
function closeEditNoteModal() {
  editNoteModal.classList.add('hidden');
}

// Format date
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleString();
}

// Event listeners
addNoteBtn.addEventListener('click', () => {
  showAddNoteForm();
});

cancelAddNoteBtn.addEventListener('click', () => {
  hideAddNoteForm();
});

addNoteForm.addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const businessId = document.getElementById('edit-business-id').value;
  const content = document.getElementById('note-content').value;
  
  if (!content.trim()) {
    showNotification('Note content cannot be empty', true);
    return;
  }
  
  try {
    showNotification('Adding note...');
    
    await notesApi.addNote(businessId, content);
    
    // Hide form
    hideAddNoteForm();
    
    showNotification('Note added successfully');
  } catch (error) {
    console.error('Error adding note:', error);
    showNotification('Failed to add note', true);
  }
});

closeEditNoteModalBtn.addEventListener('click', () => {
  closeEditNoteModal();
});

editNoteForm.addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const noteId = document.getElementById('edit-note-id').value;
  const content = document.getElementById('edit-note-content').value;
  
  if (!content.trim()) {
    showNotification('Note content cannot be empty', true);
    return;
  }
  
  try {
    showNotification('Updating note...');
    
    await notesApi.updateNote(noteId, content);
    
    // Close modal
    closeEditNoteModal();
    
    showNotification('Note updated successfully');
  } catch (error) {
    console.error('Error updating note:', error);
    showNotification('Failed to update note', true);
  }
});

deleteNoteBtn.addEventListener('click', async () => {
  const noteId = document.getElementById('edit-note-id').value;
  
  if (confirm('Are you sure you want to delete this note?')) {
    try {
      showNotification('Deleting note...');
      
      await notesApi.deleteNote(noteId);
      
      // Close modal
      closeEditNoteModal();
      
      showNotification('Note deleted successfully');
    } catch (error) {
      console.error('Error deleting note:', error);
      showNotification('Failed to delete note', true);
    }
  }
});

window.loadBusinessNotes = loadBusinessNotes;
