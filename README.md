# Prospector

## A 5-part Business Process
1. Business Finder (Stores Businesses of a particular niche and location)
2. Business Manager (Provides business management tools)
3. Business Connector (Finds contact details for key individuals)
4. Business Curator (Creates curated public directories)
5. Business Prospector (On-boarding tool)

## Business Finder

A tool to collect and store business information from Google Places API based on user-defined search criteria for near-term analysis.

### Features
- Search businesses based on dynamic criteria (keyword, ratings, country, city, website availability)
- Real-time data streaming to a front-end dashboard
- Filters to refine displayed data
- Immediate saving of business details to a Redis database
- Handles pagination for Google Places API results
- Highlights errors and incomplete data for user attention

### Tech Stack
- **Backend**: Node.js, Express.js
- **Frontend**: React
- **Database**: Redis
- **API**: Google Places API
- **Real-time Communication**: Socket.IO
- **Containerization**: Docker & Docker Compose

## Business Management Dashboard

A tool for listing, organizing, editing, and leaving notes against businesses stored in a Redis database. This dashboard serves as an effective admin maintenance service for managing business data.

### Features
- **User Authentication:** Secure user login and access control.
- **Data Fetching:** Retrieve business data from Redis.
- **Business Display:** Show businesses with details in a tabular format.
- **Search & Filter:** Enable search and filter functionalities for businesses.
- **Edit Business Details:** Allow users to edit business details.
- **Leave Notes:** Enable users to leave notes against each business.
- **Real-time Updates:** Sync changes in real-time and reflect updates instantly.

### Tech Stack
- **Backend:** Node.js, Express.js
- **Frontend:** React.js
- **Database:** Redis
- **Real-time Communication:** WebSockets
- **Authentication:** JWT, Passport.js
- **Containerization:** Docker & Docker Compose

## Installation

For **Business Finder**:
Please refer to the [Installation Guide](./INSTALLATION.md) for detailed setup instructions.

For **Business Management Dashboard**:
Please refer to the [Installation Guide](./business-management/INSTALLATION.md) for detailed setup instructions.

## Quick Start

### For **Business Finder**:
1. Ensure you have Docker and Docker Compose installed
2. Create a `.env` file with your Google Places API key:

  GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

3. Run `docker-compose up -d`
4. Access the application at [http://localhost:3005](http://localhost:3005)

### For **Business Management Dashboard**:
1. Ensure you have Docker and Docker Compose installed
2. Create a `.env` file with your relevant configurations:

   PORT=3004
   REDIS_URL=redis://redis:6379

3. Run `docker-compose up -d`
4. Access the application at [http://localhost:3004](http://localhost:3004)

## Core Flow

### For **Business Finder**:
1. User enters search criteria (keyword, country, city, ratings, website availability)
2. User sets rate limit and max results
3. System sends requests to Google Places API with specified criteria
4. System processes results, extracting relevant details
5. System checks if businesses already exist in Redis and saves new entries
6. System streams search results to the front-end dashboard in real-time
7. User can refine displayed data using available filters

### For **Business Management Dashboard**:
1. User logs in and navigates to the Business Management Dashboard.
2. The application fetches the list of businesses from the Redis database.
3. The dashboard displays the list of businesses with their details (name, address, phone number, website, rating).
4. User can search, filter, edit business details, and leave notes against each business.
5. Any changes made by the user are immediately synced back to the Redis database.
6. The dashboard updates in real-time to reflect any changes, ensuring data consistency.

## License

This project is for private use only.
