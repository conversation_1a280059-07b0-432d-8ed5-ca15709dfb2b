import React from 'react';
import './ResultsList.css';

const ResultsList = ({ results, isLoading }) => {
  if (isLoading) {
    return (
      <div className="results-loading">
        <p>Loading results...</p>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div className="no-results">
        <p>No results found. Try adjusting your search criteria.</p>
      </div>
    );
  }

  return (
    <div className="results-list">
      <h3>Search Results ({results.length})</h3>
      <div className="results-table-container">
        <table className="results-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Address</th>
              <th>Phone</th>
              <th>Website</th>
              <th>Rating</th>
              <th>Reviews</th>
            </tr>
          </thead>
          <tbody>
            {results.map((business) => (
              <tr key={business.places_id} className={!business.website ? 'incomplete-data' : ''}>
                <td>{business.name}</td>
                <td>{business.address || 'N/A'}</td>
                <td>{business.phone || 'N/A'}</td>
                <td>
                  {business.website ? (
                    <a href={business.website} target="_blank" rel="noopener noreferrer">Website</a>
                  ) : (
                    <span className="no-website">No website</span>
                  )}
                </td>
                <td>{business.rating || 'N/A'}</td>
                <td>{business.user_ratings_total || 0}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ResultsList;
