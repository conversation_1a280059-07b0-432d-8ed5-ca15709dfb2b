const { getBusinessRecords, getBusinessRecord, getEnrichmentData } = require('../config/redis');
const { logger } = require('../services/logService');

/**
 * Get all businesses with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getBusinesses(req, res, next) {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Get businesses from Redis
    const result = await getBusinessRecords(req.redisClient, page, limit);

    // Get enrichment data for each business
    for (const business of result.businesses) {
      const enrichmentData = await getEnrichmentData(req.redisClient, business.id);
      business.enrichmentStatus = Object.keys(enrichmentData).length > 0;
      business.enrichmentModules = Object.keys(enrichmentData);
      business.enrichment_data = enrichmentData; // Include full enrichment data for frontend
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error getting businesses:', error);
    next(error);
  }
}

/**
 * Get a specific business by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getBusiness(req, res, next) {
  try {
    const { businessId } = req.params;

    // Get business from Redis
    const business = await getBusinessRecord(req.redisClient, businessId);

    if (!business) {
      return res.status(404).json({
        success: false,
        error: `Business with ID ${businessId} not found`
      });
    }

    // Get enrichment data for the business
    const enrichmentData = await getEnrichmentData(req.redisClient, businessId);

    res.json({
      success: true,
      data: {
        business,
        enrichment: enrichmentData
      }
    });
  } catch (error) {
    logger.error(`Error getting business ${req.params.businessId}:`, error);
    next(error);
  }
}

module.exports = {
  getBusinesses,
  getBusiness
};
