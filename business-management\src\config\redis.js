const redis = require('redis');
const { promisify } = require('util');

// Create Redis client
const client = redis.createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  retry_strategy: function(options) {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      // End reconnecting on a specific error
      console.error('Redis server refused connection');
      return new Error('The server refused the connection');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      // End reconnecting after a specific timeout
      return new Error('Retry time exhausted');
    }
    if (options.attempt > 10) {
      // End reconnecting with built in error
      return undefined;
    }
    // Reconnect after
    return Math.min(options.attempt * 100, 3000);
  }
});

// Handle Redis client errors
client.on('error', (err) => {
  console.error('Redis error:', err);
});

// Log successful connection
client.on('connect', () => {
  console.log('Connected to Redis server successfully');
  
  // Test Redis connection by trying to get all business keys
  client.keys('business:*', (err, keys) => {
    if (err) {
      console.error('Error getting business keys:', err);
    } else {
      console.log(`Found ${keys.length} businesses in Redis`);
      console.log('Sample keys:', keys.slice(0, 5));
    }
  });
});

// Promisify Redis commands
const getAsync = promisify(client.get).bind(client);
const setAsync = promisify(client.set).bind(client);
const delAsync = promisify(client.del).bind(client);
const keysAsync = promisify(client.keys).bind(client);
const hgetallAsync = promisify(client.hgetall).bind(client);
const hsetAsync = promisify(client.hset).bind(client);
const hmsetAsync = promisify(client.hmset).bind(client);
const hdelAsync = promisify(client.hdel).bind(client);
const existsAsync = promisify(client.exists).bind(client);
const scanAsync = promisify(client.scan).bind(client);
const saddAsync = promisify(client.sadd).bind(client);
const sremAsync = promisify(client.srem).bind(client);
const smembersAsync = promisify(client.smembers).bind(client);

module.exports = {
  client,
  getAsync,
  setAsync,
  delAsync,
  keysAsync,
  hgetallAsync,
  hsetAsync,
  hmsetAsync,
  hdelAsync,
  existsAsync,
  scanAsync,
  saddAsync,
  sremAsync,
  smembersAsync
};
