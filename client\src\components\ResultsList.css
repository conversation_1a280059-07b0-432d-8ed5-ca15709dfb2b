.results-list {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.business-card {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.business-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.business-card h4 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 18px;
  color: #343a40;
}

.business-address {
  color: #6c757d;
  margin-bottom: 12px;
  font-size: 14px;
}

.business-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.business-rating {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rating-value {
  font-weight: bold;
  color: #ffc107;
  background-color: #343a40;
  padding: 2px 6px;
  border-radius: 4px;
}

.rating-count {
  font-size: 13px;
  color: #6c757d;
}

.business-phone {
  font-size: 14px;
  color: #495057;
}

.business-website {
  display: inline-block;
  background-color: #007bff;
  color: white;
  text-decoration: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  transition: background-color 0.2s;
}

.business-website:hover {
  background-color: #0069d9;
}

.no-website {
  font-size: 14px;
  color: #dc3545;
  font-style: italic;
}

.no-results, .results-loading {
  text-align: center;
  padding: 30px;
  color: #6c757d;
}

/* Highlight incomplete data with yellow background */
.incomplete-data {
  background-color: #fff3cd;
}

/* Highlight error data with pink background */
.error-data {
  background-color: #f8d7da;
}

.results-table-container {
  overflow-x: auto;
  margin-top: 15px;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-size: 15px;
}

.results-table th, .results-table td {
  padding: 10px 12px;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
}

.results-table th {
  background-color: #f1f3f5;
  font-weight: 600;
}

.results-table tr:last-child td {
  border-bottom: none;
}

.results-table tr.incomplete-data {
  background-color: #fff3cd;
}

.no-website {
  color: #dc3545;
  font-style: italic;
}
