const socketIo = require('socket.io');

// Initialize Socket.io service
const initializeSocketService = (server) => {
  const io = socketIo(server, {
    cors: {
      origin: '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE']
    }
  });

  // Socket.io connection handler
  io.on('connection', (socket) => {
    console.log('New client connected:', socket.id);
    
    // Send welcome message
    socket.emit('welcome', { message: 'Connected to Business Management Dashboard' });
    
    // Handle client disconnection
    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
    });
    
    // Handle client joining a business room
    socket.on('join:business', (businessId) => {
      socket.join(`business:${businessId}`);
      console.log(`Client ${socket.id} joined business room: ${businessId}`);
    });
    
    // Handle client leaving a business room
    socket.on('leave:business', (businessId) => {
      socket.leave(`business:${businessId}`);
      console.log(`Client ${socket.id} left business room: ${businessId}`);
    });
  });

  return io;
};

// Emit events to clients
const emitEvent = (io, event, data) => {
  io.emit(event, data);
};

// Emit events to clients in a specific room
const emitToRoom = (io, room, event, data) => {
  io.to(room).emit(event, data);
};

module.exports = {
  initializeSocketService,
  emitEvent,
  emitToRoom
};
