const winston = require('winston');

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.printf(({ timestamp, level, message }) => {
    return `${timestamp} ${level}: ${message}`;
  })
);

// Create logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

/**
 * Log enrichment action
 * @param {string} businessId - Business ID
 * @param {string} module - Module name
 * @param {string} action - Action performed
 * @param {Object} data - Additional data
 */
function logEnrichmentAction(businessId, module, action, data = {}) {
  logger.info(`Enrichment action: ${action} | Business: ${businessId} | Module: ${module} | ${JSON.stringify(data)}`);
}

/**
 * Log module error
 * @param {string} module - Module name
 * @param {string} error - Error message
 * @param {Object} data - Additional data
 */
function logModuleError(module, error, data = {}) {
  logger.error(`Module error: ${module} | ${error} | ${JSON.stringify(data)}`);
}

module.exports = {
  logger,
  logEnrichmentAction,
  logModuleError
};
