const passport = require('passport');
const express = require('express');
const router = express.Router();

// Authentication middleware
const auth = passport.authenticate('jwt', { session: false });

// Import route modules
const authRoutes = require('./auth');
const businessRoutes = require('./businesses');
const noteRoutes = require('./notes');

// Use route modules
router.use('/auth', authRoutes);
router.use('/businesses', businessRoutes);
router.use('/notes', noteRoutes);

// Health check route
router.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Business Management API is running' });
});

module.exports = router;
