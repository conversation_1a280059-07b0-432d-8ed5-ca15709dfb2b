# Node modules
node_modules/

# Environment variables and sensitive config
.env
.env.*
*.env
**/.env
**/.env*

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
**/*.log
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# OS generated files
.DS_Store
Thumbs.db

# Build output
/dist/
/build/

# VSCode settings
.vscode/

# Docker
*.local

# Backup files
*.bak
**/*.bak

# Ignore test coverage
coverage/
**/coverage/

# Ignore Redis dump files
dump.rdb

# Ignore sensitive files in business-management and client
business-management/.env
client/.env
server/.env

# Ignore local database files
*.sqlite
*.db

# Ignore secrets
secrets.*

# Ignore IDE settings
.idea/
*.iml

# Ignore miscellaneous
*.swp
*.swo

# Ignore context directory
CONTEXT/

# Ignore test output files
**/test-output/
**/test-results/
