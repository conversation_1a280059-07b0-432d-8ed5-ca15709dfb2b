import React, { useState } from 'react';
import './FilterPanel.css';

const FilterPanel = ({ filters, onFilterChange }) => {
  const [activeFilters, setActiveFilters] = useState({
    minRating: filters?.minRating || 0,
    country: filters?.country || '',
    city: filters?.city || '',
    locality: filters?.locality || '', // Add locality filter
    hasWebsite: filters?.hasWebsite || false,
    noWebsite: filters?.noWebsite || false // Add noWebsite filter
  });

  const handleFilterChange = (name, value) => {
    const updatedFilters = {
      ...activeFilters,
      [name]: value
    };
    
    setActiveFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  const clearFilters = () => {
    const resetFilters = {
      minRating: 0,
      country: '',
      city: '',
      locality: '',
      hasWebsite: false,
      noWebsite: false
    };
    
    setActiveFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  return (
    <div className="filter-panel">
      <div className="filter-header">
        <h3>Filter Results</h3>
        <button className="clear-filters-button" onClick={clearFilters}>
          Clear Filters
        </button>
      </div>
      
      <div className="filter-group">
        <label htmlFor="filter-rating">
          Minimum Rating: {activeFilters.minRating}
        </label>
        <input
          type="range"
          id="filter-rating"
          min="0"
          max="5"
          step="0.5"
          value={activeFilters.minRating}
          onChange={(e) => handleFilterChange('minRating', parseFloat(e.target.value))}
        />
      </div>
      
      <div className="filter-row">
        <div className="filter-group">
          <label htmlFor="filter-country">Country</label>
          <input
            type="text"
            id="filter-country"
            value={activeFilters.country}
            onChange={(e) => handleFilterChange('country', e.target.value)}
            placeholder="Filter by country"
          />
        </div>
        
        <div className="filter-group">
          <label htmlFor="filter-city">City</label>
          <input
            type="text"
            id="filter-city"
            value={activeFilters.city}
            onChange={(e) => handleFilterChange('city', e.target.value)}
            placeholder="Filter by city"
          />
        </div>
        
        <div className="filter-group">
          <label htmlFor="filter-locality">Locality</label>
          <input
            type="text"
            id="filter-locality"
            value={activeFilters.locality}
            onChange={(e) => handleFilterChange('locality', e.target.value)}
            placeholder="Neighborhood, District, etc."
          />
        </div>
      </div>
      
      <div className="filter-group checkbox-group">
        <input
          type="checkbox"
          id="filter-website"
          checked={activeFilters.hasWebsite}
          onChange={(e) => handleFilterChange('hasWebsite', e.target.checked)}
        />
        <label htmlFor="filter-website">Has Website</label>
        <input
          type="checkbox"
          id="filter-no-website"
          checked={activeFilters.noWebsite}
          onChange={(e) => handleFilterChange('noWebsite', e.target.checked)}
          style={{ marginLeft: '20px' }}
        />
        <label htmlFor="filter-no-website">No Website</label>
      </div>
    </div>
  );
};

export default FilterPanel;
