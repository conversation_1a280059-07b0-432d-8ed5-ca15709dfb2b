/**
 * Business Connector Validation Script
 * 
 * This script validates the core functionality of the Business Connector:
 * - Redis connectivity
 * - Module loading system
 * - API endpoints
 * - Error handling
 */

const redis = require('redis');
const axios = require('axios');
const { createRedisClient, getBusinessRecords } = require('../config/redis');
const { loadModules, getModules, validateModuleConfigs } = require('../modules');
const { logger } = require('../services/logService');

// Configuration
const API_BASE_URL = 'http://localhost:3006/api';
const TEST_BUSINESS_ID = '1'; // Replace with a valid business ID from your Redis database

/**
 * Test Redis connectivity
 */
async function testRedisConnectivity() {
  logger.info('Testing Redis connectivity...');
  
  try {
    const client = createRedisClient();
    
    // Wait for connection
    await new Promise((resolve) => {
      client.on('connect', () => {
        logger.info('✅ Redis connection successful');
        resolve();
      });
      
      client.on('error', (err) => {
        logger.error('❌ Redis connection failed:', err);
        process.exit(1);
      });
    });
    
    // Test basic operations
    await client.set('test:key', 'test:value');
    const value = await client.get('test:key');
    
    if (value === 'test:value') {
      logger.info('✅ Redis operations successful');
    } else {
      logger.error('❌ Redis operations failed');
      process.exit(1);
    }
    
    // Clean up
    await client.del('test:key');
    
    // Test business record retrieval
    const result = await getBusinessRecords(client, 1, 5);
    logger.info(`✅ Retrieved ${result.businesses.length} business records`);
    
    // Close connection
    await client.quit();
    
    return client;
  } catch (error) {
    logger.error('❌ Redis test failed:', error);
    process.exit(1);
  }
}

/**
 * Test module loading system
 */
async function testModuleLoading() {
  logger.info('Testing module loading system...');
  
  try {
    // Load modules
    await loadModules();
    
    // Get loaded modules
    const modules = getModules();
    
    if (modules.size > 0) {
      logger.info(`✅ Loaded ${modules.size} modules`);
      
      // Log module names
      for (const [name, module] of modules.entries()) {
        logger.info(`  - ${module.metadata.name} (${name})`);
      }
      
      // Validate module configurations
      const validationResults = await validateModuleConfigs();
      
      for (const [name, isValid] of Object.entries(validationResults)) {
        if (isValid) {
          logger.info(`✅ Module ${name} configuration is valid`);
        } else {
          logger.warn(`⚠️ Module ${name} configuration is invalid`);
        }
      }
    } else {
      logger.error('❌ No modules loaded');
      process.exit(1);
    }
  } catch (error) {
    logger.error('❌ Module loading test failed:', error);
    process.exit(1);
  }
}

/**
 * Test API endpoints
 */
async function testApiEndpoints() {
  logger.info('Testing API endpoints...');
  
  try {
    // Test GET /api/businesses
    const businessesResponse = await axios.get(`${API_BASE_URL}/businesses`);
    
    if (businessesResponse.status === 200 && businessesResponse.data.success) {
      logger.info('✅ GET /api/businesses successful');
    } else {
      logger.error('❌ GET /api/businesses failed');
      process.exit(1);
    }
    
    // Test GET /api/enrichment/modules
    const modulesResponse = await axios.get(`${API_BASE_URL}/enrichment/modules`);
    
    if (modulesResponse.status === 200 && modulesResponse.data.success) {
      logger.info('✅ GET /api/enrichment/modules successful');
      logger.info(`  - Found ${modulesResponse.data.data.length} modules`);
    } else {
      logger.error('❌ GET /api/enrichment/modules failed');
      process.exit(1);
    }
    
    // Test GET /api/businesses/:id
    try {
      const businessResponse = await axios.get(`${API_BASE_URL}/businesses/${TEST_BUSINESS_ID}`);
      
      if (businessResponse.status === 200 && businessResponse.data.success) {
        logger.info(`✅ GET /api/businesses/${TEST_BUSINESS_ID} successful`);
      } else {
        logger.warn(`⚠️ GET /api/businesses/${TEST_BUSINESS_ID} returned unexpected response`);
      }
    } catch (error) {
      logger.warn(`⚠️ GET /api/businesses/${TEST_BUSINESS_ID} failed: ${error.message}`);
    }
    
    // Test GET /api/enrichment/:businessId
    try {
      const enrichmentResponse = await axios.get(`${API_BASE_URL}/enrichment/${TEST_BUSINESS_ID}`);
      
      if (enrichmentResponse.status === 200 && enrichmentResponse.data.success) {
        logger.info(`✅ GET /api/enrichment/${TEST_BUSINESS_ID} successful`);
      } else {
        logger.warn(`⚠️ GET /api/enrichment/${TEST_BUSINESS_ID} returned unexpected response`);
      }
    } catch (error) {
      logger.warn(`⚠️ GET /api/enrichment/${TEST_BUSINESS_ID} failed: ${error.message}`);
    }
  } catch (error) {
    logger.error('❌ API endpoints test failed:', error);
    process.exit(1);
  }
}

/**
 * Test error handling
 */
async function testErrorHandling() {
  logger.info('Testing error handling...');
  
  try {
    // Test 404 Not Found
    try {
      await axios.get(`${API_BASE_URL}/nonexistent-endpoint`);
      logger.error('❌ 404 Not Found test failed: Expected error but got success');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        logger.info('✅ 404 Not Found test successful');
      } else {
        logger.error('❌ 404 Not Found test failed:', error);
      }
    }
    
    // Test invalid business ID
    try {
      await axios.get(`${API_BASE_URL}/businesses/invalid-id`);
      logger.warn('⚠️ Invalid business ID test: Expected error but got success');
    } catch (error) {
      if (error.response && (error.response.status === 404 || error.response.status === 400)) {
        logger.info('✅ Invalid business ID test successful');
      } else {
        logger.error('❌ Invalid business ID test failed:', error);
      }
    }
    
    // Test invalid module name
    try {
      await axios.get(`${API_BASE_URL}/enrichment/${TEST_BUSINESS_ID}/nonexistent-module`);
      logger.warn('⚠️ Invalid module name test: Expected error but got success');
    } catch (error) {
      if (error.response && (error.response.status === 404 || error.response.status === 400)) {
        logger.info('✅ Invalid module name test successful');
      } else {
        logger.error('❌ Invalid module name test failed:', error);
      }
    }
  } catch (error) {
    logger.error('❌ Error handling test failed:', error);
    process.exit(1);
  }
}

/**
 * Run all validation tests
 */
async function runValidation() {
  logger.info('Starting Business Connector validation...');
  
  try {
    // Test Redis connectivity
    await testRedisConnectivity();
    
    // Test module loading system
    await testModuleLoading();
    
    // Test API endpoints
    await testApiEndpoints();
    
    // Test error handling
    await testErrorHandling();
    
    logger.info('✅ Business Connector validation completed successfully');
  } catch (error) {
    logger.error('❌ Validation failed:', error);
    process.exit(1);
  }
}

// Run validation
runValidation();
