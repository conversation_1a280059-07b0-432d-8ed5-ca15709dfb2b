const express = require('express');
const router = express.Router();
const passport = require('passport');
const redisService = require('../services/redisService');

// Authentication middleware
const auth = passport.authenticate('jwt', { session: false });

// @route   GET api/notes/business/:businessId
// @desc    Get all notes for a specific business
// @access  Private
router.get('/business/:businessId', auth, async (req, res) => {
  try {
    const notes = await redisService.getNotesByBusinessId(req.params.businessId);
    res.json(notes);
  } catch (err) {
    console.error('Error fetching notes:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST api/notes/business/:businessId
// @desc    Add a new note to a business
// @access  Private
router.post('/business/:businessId', auth, async (req, res) => {
  try {
    const { content } = req.body;
    
    // Validate input
    if (!content) {
      return res.status(400).json({ message: 'Please provide note content' });
    }
    
    // Check if business exists
    const business = await redisService.getBusinessById(req.params.businessId);
    if (!business) {
      return res.status(404).json({ message: 'Business not found' });
    }
    
    // Create note
    const note = {
      id: Date.now().toString(),
      businessId: req.params.businessId,
      content,
      createdBy: req.user.username,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    await redisService.addNote(note);
    
    // Emit socket event for real-time updates
    req.io.emit('note:add', { businessId: req.params.businessId, note });
    
    res.status(201).json(note);
  } catch (err) {
    console.error('Error adding note:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT api/notes/:id
// @desc    Update a specific note
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { content } = req.body;
    
    // Validate input
    if (!content) {
      return res.status(400).json({ message: 'Please provide note content' });
    }
    
    // Check if note exists
    const existingNote = await redisService.getNoteById(req.params.id);
    if (!existingNote) {
      return res.status(404).json({ message: 'Note not found' });
    }
    
    // Update note
    const updatedNote = {
      ...existingNote,
      content,
      updatedAt: new Date().toISOString()
    };
    
    await redisService.updateNote(req.params.id, updatedNote);
    
    // Emit socket event for real-time updates
    req.io.emit('note:update', { id: req.params.id, note: updatedNote });
    
    res.json(updatedNote);
  } catch (err) {
    console.error('Error updating note:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE api/notes/:id
// @desc    Delete a specific note
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if note exists
    const existingNote = await redisService.getNoteById(req.params.id);
    if (!existingNote) {
      return res.status(404).json({ message: 'Note not found' });
    }
    
    // Delete note
    await redisService.deleteNote(req.params.id);
    
    // Emit socket event for real-time updates
    req.io.emit('note:delete', { id: req.params.id, businessId: existingNote.businessId });
    
    res.json({ message: 'Note deleted successfully' });
  } catch (err) {
    console.error('Error deleting note:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
