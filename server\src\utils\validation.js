// Validation utilities
const validateInput = {
  // Validate search parameters
  searchParams: (params) => {
    const errors = [];
    const { keyword, country, city, locality, minRating, hasWebsite, noWebsite, rateLimit, maxResults } = params;
    
    if (!keyword || keyword.trim() === '') {
      errors.push('Keyword is required');
    }
    
    if (minRating !== undefined && (isNaN(minRating) || minRating < 0 || minRating > 5)) {
      errors.push('Min rating must be a number between 0 and 5');
    }
    
    if (rateLimit !== undefined && (isNaN(rateLimit) || rateLimit < 1)) {
      errors.push('Rate limit must be a positive number');
    }
    
    if (maxResults !== undefined && (isNaN(maxResults) || maxResults < 1 || maxResults > 200)) {
      errors.push('Max results must be a positive number not exceeding 200');
    }
    // No validation for locality or noWebsite needed
    return {
      isValid: errors.length === 0,
      errors
    };
  },
  
  // Sanitize input to prevent injection
  sanitize: (input) => {
    if (typeof input === 'string') {
      // Basic sanitization - remove HTML tags and trim
      return input.replace(/<[^>]*>?/gm, '').trim();
    }
    return input;
  },
  
  // Create a query string for Google Places API
  createQueryString: (params) => {
    const { keyword, country, city, locality } = params;
    let query = keyword;
    if (locality) {
      query += ` in ${locality}`;
    } else if (city) {
      query += ` in ${city}`;
    }
    if (country) {
      query += ` ${country}`;
    }
    return query;
  }
};

module.exports = validateInput;
