// Defensive global assignment for API_BASE_URL
if (!window.API_BASE_URL) {
  window.API_BASE_URL = '/api';
}

// Generic API request function with authentication
async function apiRequest(endpoint, method = 'GET', data = null) {
  const headers = {
    'Content-Type': 'application/json'
  };
  
  // Add auth token if available
  if (getAuthToken()) {
    headers['Authorization'] = getAuthToken();
  }
  
  const options = {
    method,
    headers
  };
  
  // Add body for non-GET requests
  if (data && method !== 'GET') {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(`${window.API_BASE_URL}${endpoint}`, options);
    
    // Handle unauthorized
    if (response.status === 401) {
      logout();
      throw new Error('Your session has expired. Please log in again.');
    }
    
    const responseData = await response.json();
    
    if (!response.ok) {
      throw new Error(responseData.message || 'API request failed');
    }
    
    return responseData;
  } catch (error) {
    console.error(`API error (${endpoint}):`, error);
    throw error;
  }
}

// Defensive global assignment for businessesApi
if (!window.businessesApi) {
  window.businessesApi = {
    // Get businesses with pagination and filtering
    getBusinesses: async (page = 1, limit = 10, filters = {}) => {
      // Build query string with new filter parameters
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...Object.fromEntries(
          Object.entries({
            ...filters,
            // Convert boolean values to strings for URL
            ...(filters.hasWebsite !== undefined && { hasWebsite: filters.hasWebsite.toString() }),
            ...(filters.hasPhone !== undefined && { hasPhone: filters.hasPhone.toString() }),
            ...(filters.maxRating && { maxRating: filters.maxRating }),
            ...(filters.minRatings !== undefined && { minRatings: filters.minRatings.toString() }),
            ...(filters.maxRatings !== undefined && { maxRatings: filters.maxRatings.toString() })
          }).filter(([_, value]) => value !== undefined && value !== '')
        )
      });
      
      return apiRequest(`/businesses?${queryParams.toString()}`);
    },
    
    // Get a specific business
    getBusiness: async (id) => {
      return apiRequest(`/businesses/${id}`);
    },
    
    // Update a business
    updateBusiness: async (id, data) => {
      return apiRequest(`/businesses/${id}`, 'PUT', data);
    },
    
    // Delete a business
    deleteBusiness: async (id) => {
      return apiRequest(`/businesses/${id}`, 'DELETE');
    }
  };
}

// Defensive global assignment for notesApi
if (!window.notesApi) {
  window.notesApi = {
    // Get notes for a business
    getBusinessNotes: async (businessId) => {
      return apiRequest(`/notes/business/${businessId}`);
    },
    
    // Add a note to a business
    addNote: async (businessId, content) => {
      return apiRequest(`/notes/business/${businessId}`, 'POST', { content });
    },
    
    // Update a note
    updateNote: async (id, content) => {
      return apiRequest(`/notes/${id}`, 'PUT', { content });
    },
    
    // Delete a note
    deleteNote: async (id) => {
      return apiRequest(`/notes/${id}`, 'DELETE');
    }
  };
}
