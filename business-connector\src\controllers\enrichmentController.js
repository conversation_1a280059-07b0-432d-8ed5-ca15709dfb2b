const { getBusinessRecord, getEnrichmentData, saveEnrichmentData } = require('../config/redis');
const { getModule, validateModuleConfigs, getAvailableModules } = require('../modules'); // Added getAvailableModules
const { logger, logEnrichmentAction, logModuleError } = require('../services/logService');

/**
 * Get all available enrichment modules
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function listAvailableModules(req, res, next) {
  try {
    const availableModules = getAvailableModules();
    res.json({
      success: true,
      data: availableModules
    });
  } catch (error) {
    logger.error('Error listing available modules:', error);
    next(error);
  }
}

/**
 * Get all enrichment data for a business
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getBusinessEnrichment(req, res, next) {
  try {
    const { businessId } = req.params;

    // Check if business exists
    const business = await getBusinessRecord(req.redisClient, businessId);

    if (!business) {
      return res.status(404).json({
        success: false,
        error: `Business with ID ${businessId} not found`
      });
    }

    // Get enrichment data
    const enrichmentData = await getEnrichmentData(req.redisClient, businessId);

    res.json({
      success: true,
      data: enrichmentData
    });
  } catch (error) {
    logger.error(`Error getting enrichment data for business ${req.params.businessId}:`, error);
    next(error);
  }
}

/**
 * Get specific module enrichment for a business
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getModuleEnrichment(req, res, next) {
  try {
    const { businessId, module: moduleName } = req.params;

    // Check if business exists
    const business = await getBusinessRecord(req.redisClient, businessId);

    if (!business) {
      return res.status(404).json({
        success: false,
        error: `Business with ID ${businessId} not found`
      });
    }

    // Check if module exists
    const module = getModule(moduleName);

    if (!module) {
      return res.status(404).json({
        success: false,
        error: `Module ${moduleName} not found`
      });
    }

    // Get enrichment data
    const enrichmentData = await getEnrichmentData(req.redisClient, businessId, moduleName);

    res.json({
      success: true,
      data: enrichmentData || {}
    });
  } catch (error) {
    logger.error(`Error getting ${req.params.module} enrichment for business ${req.params.businessId}:`, error);
    next(error);
  }
}

/**
 * Run enrichment for a business using a specific module
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function runEnrichment(req, res, next) {
  try {
    const { businessId, module: moduleName } = req.params;

    // Check if business exists
    const business = await getBusinessRecord(req.redisClient, businessId);

    if (!business) {
      return res.status(404).json({
        success: false,
        error: `Business with ID ${businessId} not found`
      });
    }

    // Check if module exists
    const module = getModule(moduleName);

    if (!module) {
      return res.status(404).json({
        success: false,
        error: `Module ${moduleName} not found`
      });
    }

    // Validate module configuration
    const isConfigured = await module.validateConfig();

    if (!isConfigured) {
      return res.status(400).json({
        success: false,
        error: `Module ${moduleName} is not properly configured`
      });
    }

    // Run enrichment
    logEnrichmentAction(businessId, moduleName, 'start');
    const enrichmentData = await module.enrich(business);

    if (enrichmentData.error) {
      logModuleError(moduleName, `Enrichment failed: ${enrichmentData.error}`, { businessId });
      return res.status(400).json({
        success: false,
        error: enrichmentData.error
      });
    }

    logEnrichmentAction(businessId, moduleName, 'complete', { dataFields: Object.keys(enrichmentData) });

    res.json({
      success: true,
      data: enrichmentData
    });
  } catch (error) {
    logModuleError(req.params.module, `Enrichment error: ${error.message}`, { businessId: req.params.businessId });
    next(error);
  }
}

/**
 * Update enrichment data for a business
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function updateEnrichment(req, res, next) {
  try {
    const { businessId, module: moduleName } = req.params;
    const { data } = req.body;

    // Check if business exists
    const business = await getBusinessRecord(req.redisClient, businessId);

    if (!business) {
      return res.status(404).json({
        success: false,
        error: `Business with ID ${businessId} not found`
      });
    }

    // Check if module exists
    const module = getModule(moduleName);

    if (!module) {
      return res.status(404).json({
        success: false,
        error: `Module ${moduleName} not found`
      });
    }

    // Save enrichment data
    await saveEnrichmentData(req.redisClient, businessId, moduleName, data);

    logEnrichmentAction(businessId, moduleName, 'update', { dataFields: Object.keys(data) });

    res.json({
      success: true,
      message: `Enrichment data for business ${businessId} using module ${moduleName} updated successfully`
    });
  } catch (error) {
    logger.error(`Error updating enrichment data for business ${req.params.businessId}:`, error);
    next(error);
  }
}

module.exports = {
  listAvailableModules, // Added
  getBusinessEnrichment,
  getModuleEnrichment,
  runEnrichment,
  updateEnrichment
};
