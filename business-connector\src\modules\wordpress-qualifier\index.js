/**
 * WordPress Qualifier Enrichment Module
 *
 * This module analyzes website HTML to determine the likelihood that a site
 * is built with WordPress by looking for tell-tale signs and patterns.
 */

const axios = require('axios');
const config = require('./config');
const { logger, logModuleError } = require('../../services/logService');

/**
 * WordPress detection patterns organized by likelihood level
 */
const DETECTION_PATTERNS = {
  extreme: {
    htmlClasses: [
      'wp-footer', 'wp-caption', 'wp-block', 'wp-site-blocks',
      'wp-block-group', 'wp-block-columns', 'wp-block-image',
      'wp-block-paragraph', 'wp-block-heading', 'wp-block-list'
    ],
    htmlIds: [
      'wp-toolbar', 'wp-admin-bar', 'wp-content', 'wp-footer'
    ]
  },
  high: {
    urlPatterns: [
      '/wp-content/', '/wp-admin/', '/wp-includes/',
      '/xmlrpc.php', '/wp-json/', '/wp-login.php'
    ],
    permalinkPatterns: [
      /\/\d{4}\/\d{2}\/[^\/]+\/?$/,  // /year/month/postname/
      /\/category\/[^\/]+\/?$/,       // /category/postname/
      /\/tag\/[^\/]+\/?$/,            // /tag/tagname/
      /\/author\/[^\/]+\/?$/          // /author/username/
    ]
  },
  medium: {
    jsFiles: [
      '/wp-includes/js/', '/wp-content/themes/', '/wp-content/plugins/',
      'jquery.js', 'wp-embed.min.js', 'wp-emoji-release.min.js'
    ],
    formFields: [
      'comment', 'author', 'email', 'url', 'log', 'pwd', 'wp-submit',
      'comment_post_ID', 'comment_parent'
    ]
  },
  low: {
    metaTags: [
      'generator.*wordpress', 'powered.*wordpress'
    ],
    comments: [
      'This site is powered by WordPress',
      'WordPress.com',
      'wp-content',
      'wp-includes'
    ],
    queryStrings: [
      '?p=', '?cat=', '?m=', '?s=', '?tag=', '?author='
    ]
  }
};

/**
 * Extract domain from URL
 * @param {string} url - Website URL
 * @returns {string|null} Domain or null if invalid
 */
function extractDomain(url) {
  try {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    return null;
  }
}

/**
 * Normalize URL for analysis
 * @param {string} url - Website URL
 * @returns {string} Normalized URL
 */
function normalizeUrl(url) {
  try {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    return url;
  } catch (error) {
    return url;
  }
}

/**
 * Fetch website HTML content with smart content limiting
 * @param {string} url - Website URL
 * @returns {Promise<string>} HTML content (potentially truncated)
 */
async function fetchWebsiteContent(url) {
  try {
    const normalizedUrl = normalizeUrl(url);

    const response = await axios.get(normalizedUrl, {
      timeout: config.getRequestTimeout(),
      maxContentLength: config.getMaxContentSize(),
      maxRedirects: config.getMaxRedirects(),
      headers: {
        'User-Agent': config.getUserAgent(),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive'
      },
      validateStatus: function (status) {
        return status >= 200 && status < 400; // Accept 2xx and 3xx status codes
      },
      responseType: 'text'
    });

    let htmlContent = response.data;

    // If content is very large, truncate it but ensure we get the head and some body content
    const maxAnalysisSize = 2 * 1024 * 1024; // 2MB for analysis
    if (htmlContent.length > maxAnalysisSize) {
      // Try to get head section and first part of body
      const headMatch = htmlContent.match(/<head[\s\S]*?<\/head>/i);
      const bodyStart = htmlContent.indexOf('<body');

      if (headMatch && bodyStart !== -1) {
        // Take full head + first 1MB of body content
        const headContent = headMatch[0];
        const bodyContent = htmlContent.substring(bodyStart, bodyStart + (1024 * 1024));
        htmlContent = headContent + '\n' + bodyContent;
      } else {
        // Fallback: just take first 2MB
        htmlContent = htmlContent.substring(0, maxAnalysisSize);
      }

      logger.info(`Truncated large website content for analysis: ${url} (original: ${response.data.length} bytes, analyzed: ${htmlContent.length} bytes)`);
    }

    return htmlContent;
  } catch (error) {
    // Handle specific axios errors
    if (error.code === 'ECONNABORTED') {
      throw new Error(`Request timeout: Website took longer than ${config.getRequestTimeout()}ms to respond`);
    } else if (error.message.includes('maxContentLength')) {
      throw new Error(`Website content too large: Exceeds ${Math.round(config.getMaxContentSize() / 1024 / 1024)}MB limit`);
    } else if (error.response) {
      throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`);
    } else if (error.request) {
      throw new Error(`Network error: Unable to reach website`);
    } else {
      throw new Error(`Failed to fetch website content: ${error.message}`);
    }
  }
}

/**
 * Check for extreme likelihood indicators
 * @param {string} html - HTML content
 * @returns {Object} Detection results
 */
function checkExtremeIndicators(html) {
  const results = {
    found: [],
    score: 0
  };

  // Check for WordPress-specific HTML classes
  DETECTION_PATTERNS.extreme.htmlClasses.forEach(className => {
    const regex = new RegExp(`class="[^"]*\\b${className}\\b[^"]*"`, 'i');
    if (regex.test(html)) {
      results.found.push(`WordPress class: ${className}`);
      results.score += 10;
    }
  });

  // Check for WordPress-specific HTML IDs
  DETECTION_PATTERNS.extreme.htmlIds.forEach(idName => {
    const regex = new RegExp(`id="[^"]*\\b${idName}\\b[^"]*"`, 'i');
    if (regex.test(html)) {
      results.found.push(`WordPress ID: ${idName}`);
      results.score += 10;
    }
  });

  return results;
}

/**
 * Check for high likelihood indicators
 * @param {string} html - HTML content
 * @param {string} url - Original URL
 * @returns {Object} Detection results
 */
function checkHighIndicators(html, url) {
  const results = {
    found: [],
    score: 0
  };

  // Check for WordPress URL patterns
  DETECTION_PATTERNS.high.urlPatterns.forEach(pattern => {
    if (html.includes(pattern)) {
      results.found.push(`WordPress URL pattern: ${pattern}`);
      results.score += 8;
    }
  });

  // Check for WordPress permalink patterns in links
  const linkRegex = /href="([^"]+)"/gi;
  let match;
  while ((match = linkRegex.exec(html)) !== null) {
    const linkUrl = match[1];
    DETECTION_PATTERNS.high.permalinkPatterns.forEach(pattern => {
      if (pattern.test(linkUrl)) {
        results.found.push(`WordPress permalink pattern: ${linkUrl}`);
        results.score += 6;
      }
    });
  }

  return results;
}

/**
 * Check for medium likelihood indicators
 * @param {string} html - HTML content
 * @returns {Object} Detection results
 */
function checkMediumIndicators(html) {
  const results = {
    found: [],
    score: 0
  };

  // Check for WordPress JavaScript files
  DETECTION_PATTERNS.medium.jsFiles.forEach(jsPattern => {
    if (html.includes(jsPattern)) {
      results.found.push(`WordPress JS pattern: ${jsPattern}`);
      results.score += 4;
    }
  });

  // Check for WordPress form fields
  DETECTION_PATTERNS.medium.formFields.forEach(fieldName => {
    const regex = new RegExp(`name="[^"]*\\b${fieldName}\\b[^"]*"`, 'i');
    if (regex.test(html)) {
      results.found.push(`WordPress form field: ${fieldName}`);
      results.score += 3;
    }
  });

  return results;
}

/**
 * Check for low likelihood indicators
 * @param {string} html - HTML content
 * @returns {Object} Detection results
 */
function checkLowIndicators(html) {
  const results = {
    found: [],
    score: 0
  };

  // Check for WordPress meta tags
  DETECTION_PATTERNS.low.metaTags.forEach(metaPattern => {
    const regex = new RegExp(metaPattern, 'i');
    if (regex.test(html)) {
      results.found.push(`WordPress meta tag pattern: ${metaPattern}`);
      results.score += 2;
    }
  });

  // Check for WordPress comments
  DETECTION_PATTERNS.low.comments.forEach(commentPattern => {
    const regex = new RegExp(`<!--[^>]*${commentPattern}[^>]*-->`, 'i');
    if (regex.test(html)) {
      results.found.push(`WordPress comment: ${commentPattern}`);
      results.score += 2;
    }
  });

  // Check for WordPress query strings
  DETECTION_PATTERNS.low.queryStrings.forEach(queryPattern => {
    if (html.includes(queryPattern)) {
      results.found.push(`WordPress query pattern: ${queryPattern}`);
      results.score += 1;
    }
  });

  return results;
}

/**
 * Determine WordPress likelihood based on detection results
 * @param {Object} detectionResults - Combined detection results
 * @returns {string} Likelihood level
 */
function determineLikelihood(detectionResults) {
  const totalScore = detectionResults.extreme.score +
                    detectionResults.high.score +
                    detectionResults.medium.score +
                    detectionResults.low.score;

  if (detectionResults.extreme.score > 0) {
    return 'extreme';
  } else if (detectionResults.high.score >= 8) {
    return 'high';
  } else if (detectionResults.medium.score >= 6 || totalScore >= 10) {
    return 'medium';
  } else if (detectionResults.low.score > 0 || totalScore > 0) {
    return 'low';
  } else {
    return 'none';
  }
}

/**
 * Perform optimized WordPress detection with early termination
 * @param {string} html - HTML content
 * @param {string} url - Original URL
 * @returns {Object} Detection results
 */
function performWordPressDetection(html, url) {
  const detectionResults = {
    extreme: { found: [], score: 0 },
    high: { found: [], score: 0 },
    medium: { found: [], score: 0 },
    low: { found: [], score: 0 }
  };

  // Check extreme indicators first (most definitive)
  detectionResults.extreme = checkExtremeIndicators(html);

  // If we found extreme indicators, we can be confident it's WordPress
  if (detectionResults.extreme.score > 0) {
    logger.info(`WordPress detection: Found extreme indicators, confidence is very high`);
    return detectionResults;
  }

  // Check high likelihood indicators
  detectionResults.high = checkHighIndicators(html, url);

  // If we have strong high indicators, we might not need to check further
  if (detectionResults.high.score >= 16) { // Very strong high indicators
    logger.info(`WordPress detection: Found strong high-confidence indicators`);
    return detectionResults;
  }

  // Check medium indicators
  detectionResults.medium = checkMediumIndicators(html);

  // Check low indicators
  detectionResults.low = checkLowIndicators(html);

  return detectionResults;
}

module.exports = {
  metadata: {
    name: 'WordPress Qualifier',
    description: 'Analyzes website HTML to determine the likelihood that a site is built with WordPress',
    version: '1.0.0',
    author: 'Business Connector Team'
  },

  /**
   * Validate module configuration
   * @returns {Promise<boolean>} Always true as no external dependencies
   */
  validateConfig: async function() {
    // No external API keys required for this module
    logger.info('WordPress Qualifier module configuration validated');
    return true;
  },

  /**
   * Enrich a business record with WordPress detection data
   * @param {Object} business - Business record to enrich
   * @returns {Promise<Object>} Enrichment data
   */
  enrich: async function(business) {
    try {
      if (!business.website) {
        return {
          error: 'Business has no website URL to analyze',
          likelihood: 'none',
          confidence_score: 0
        };
      }

      logger.info(`Analyzing WordPress likelihood for business ${business.id} website: ${business.website}`);

      // Fetch website content
      const html = await fetchWebsiteContent(business.website);

      // Run optimized detection with early termination
      const detectionResults = performWordPressDetection(html, business.website);

      // Determine overall likelihood
      const likelihood = determineLikelihood(detectionResults);
      const totalScore = detectionResults.extreme.score +
                        detectionResults.high.score +
                        detectionResults.medium.score +
                        detectionResults.low.score;

      // Compile all found indicators
      const allIndicators = [
        ...detectionResults.extreme.found,
        ...detectionResults.high.found,
        ...detectionResults.medium.found,
        ...detectionResults.low.found
      ];

      const enrichmentData = {
        website_url: business.website,
        wordpress_likelihood: likelihood,
        confidence_score: totalScore,
        indicators_found: allIndicators,
        detection_breakdown: {
          extreme_indicators: detectionResults.extreme.found,
          high_indicators: detectionResults.high.found,
          medium_indicators: detectionResults.medium.found,
          low_indicators: detectionResults.low.found
        },
        analysis_timestamp: new Date().toISOString(),
        enrichment_status: 'completed',
        enrichment_source: 'WordPress Qualifier',
        match_confidence: likelihood === 'extreme' ? 0.95 :
                         likelihood === 'high' ? 0.85 :
                         likelihood === 'medium' ? 0.65 :
                         likelihood === 'low' ? 0.35 : 0.0
      };

      logger.info(`WordPress analysis completed for ${business.website}: ${likelihood} likelihood (score: ${totalScore})`);
      return enrichmentData;

    } catch (error) {
      logModuleError('wordpress-qualifier', `Enrichment error: ${error.message}`, { businessId: business.id });
      return {
        error: error.message || 'Unknown error during WordPress analysis',
        likelihood: 'error',
        confidence_score: 0
      };
    }
  }
};
