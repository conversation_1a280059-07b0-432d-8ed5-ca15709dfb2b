const redis = require('redis');
const { logger } = require('../services/logService');

/**
 * Create and configure Redis client
 * @returns {Object} Redis client
 */
function createRedisClient() {
  const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

  const client = redis.createClient({
    url: redisUrl
  });

  client.on('connect', () => {
    logger.info('Redis client connected');
  });

  client.on('error', (err) => {
    logger.error('Redis client error:', err);
  });

  // Connect to Redis
  (async () => {
    try {
      await client.connect();
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
    }
  })();

  return client;
}

/**
 * Get a business record from Redis
 * @param {Object} client - Redis client
 * @param {string} businessId - Business ID
 * @returns {Promise<Object>} Business record
 */
async function getBusinessRecord(client, businessId) {
  try {
    const business = await client.hGetAll(`business:${businessId}`);

    if (Object.keys(business).length === 0) {
      return null;
    }

    // Add the ID to the business object
    business.id = businessId;

    return business;
  } catch (error) {
    logger.error(`Error getting business record ${businessId}:`, error);
    throw error;
  }
}

/**
 * Get all business records from Redis with pagination
 * @param {Object} client - Redis client
 * @param {number} page - Page number
 * @param {number} limit - Number of records per page
 * @returns {Promise<Object>} Paginated business records
 */
async function getBusinessRecords(client, page = 1, limit = 10) {
  try {
    // Get all business keys
    const keys = await client.keys('business:*');

    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const totalPages = Math.ceil(keys.length / limit);

    // Get paginated keys
    const paginatedKeys = keys.slice(startIndex, endIndex);

    // Get business records for paginated keys
    const businesses = [];
    for (const key of paginatedKeys) {
      const businessId = key.split(':')[1];
      const business = await getBusinessRecord(client, businessId);

      if (business) {
        businesses.push(business);
      }
    }

    return {
      businesses,
      pagination: {
        total: keys.length,
        page,
        limit,
        pages: totalPages
      }
    };
  } catch (error) {
    logger.error('Error getting business records:', error);
    throw error;
  }
}

/**
 * Parse JSON strings back to objects for enrichment data
 * @param {Object} data - Raw data from Redis
 * @returns {Object} Parsed data
 */
function parseEnrichmentData(data) {
  const parsedData = {};
  for (const [key, value] of Object.entries(data)) {
    try {
      // Try to parse as JSON, if it fails, keep as string
      parsedData[key] = JSON.parse(value);
    } catch (e) {
      // If parsing fails, it's probably a string value
      parsedData[key] = value;
    }
  }
  return parsedData;
}

/**
 * Get enrichment data for a business
 * @param {Object} client - Redis client
 * @param {string} businessId - Business ID
 * @param {string} module - Module name (optional)
 * @returns {Promise<Object>} Enrichment data
 */
async function getEnrichmentData(client, businessId, module = null) {
  try {
    if (module) {
      // Get enrichment data for a specific module
      const enrichmentKey = `connector:enrichment:${businessId}:${module}`;
      const enrichmentData = await client.hGetAll(enrichmentKey);

      if (Object.keys(enrichmentData).length > 0) {
        return parseEnrichmentData(enrichmentData);
      }
      return null;
    } else {
      // Get all enrichment data for the business
      const enrichmentKeys = await client.keys(`connector:enrichment:${businessId}:*`);

      if (enrichmentKeys.length === 0) {
        return {};
      }

      const enrichmentData = {};

      for (const key of enrichmentKeys) {
        const moduleName = key.split(':')[3];
        const moduleData = await client.hGetAll(key);

        if (Object.keys(moduleData).length > 0) {
          enrichmentData[moduleName] = parseEnrichmentData(moduleData);
        }
      }

      return enrichmentData;
    }
  } catch (error) {
    logger.error(`Error getting enrichment data for business ${businessId}:`, error);
    throw error;
  }
}

/**
 * Save enrichment data for a business
 * @param {Object} client - Redis client
 * @param {string} businessId - Business ID
 * @param {string} module - Module name
 * @param {Object} data - Enrichment data
 * @returns {Promise<boolean>} Success status
 */
async function saveEnrichmentData(client, businessId, module, data) {
  try {
    const enrichmentKey = `connector:enrichment:${businessId}:${module}`;

    // Add timestamp to data
    data.timestamp = new Date().toISOString();

    // Convert objects to JSON strings for Redis storage
    const redisData = {};
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'object' && value !== null) {
        redisData[key] = JSON.stringify(value);
      } else {
        redisData[key] = value;
      }
    }

    // Save enrichment data
    await client.hSet(enrichmentKey, redisData);

    logger.info(`Saved enrichment data for business ${businessId} using module ${module}`);
    return true;
  } catch (error) {
    logger.error(`Error saving enrichment data for business ${businessId}:`, error);
    throw error;
  }
}

module.exports = {
  createRedisClient,
  getBusinessRecord,
  getBusinessRecords,
  getEnrichmentData,
  saveEnrichmentData
};
