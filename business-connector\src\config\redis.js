const redis = require('redis');
const { logger } = require('../services/logService');

/**
 * Create and configure Redis client
 * @returns {Object} Redis client
 */
function createRedisClient() {
  const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

  const client = redis.createClient({
    url: redisUrl
  });

  client.on('connect', () => {
    logger.info('Redis client connected');
  });

  client.on('error', (err) => {
    logger.error('Redis client error:', err);
  });

  // Connect to Redis
  (async () => {
    try {
      await client.connect();
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
    }
  })();

  return client;
}

/**
 * Get a business record from Redis
 * @param {Object} client - Redis client
 * @param {string} businessId - Business ID
 * @returns {Promise<Object>} Business record
 */
async function getBusinessRecord(client, businessId) {
  try {
    const business = await client.hGetAll(`business:${businessId}`);

    if (Object.keys(business).length === 0) {
      return null;
    }

    // Add the ID to the business object
    business.id = businessId;

    return business;
  } catch (error) {
    logger.error(`Error getting business record ${businessId}:`, error);
    throw error;
  }
}

/**
 * Get all business records from Redis with pagination and sorting
 * @param {Object} client - Redis client
 * @param {number} page - Page number
 * @param {number} limit - Number of records per page
 * @param {string} sortField - Field to sort by
 * @param {string} sortDirection - Sort direction ('asc' or 'desc')
 * @returns {Promise<Object>} Paginated business records
 */
async function getBusinessRecords(client, page = 1, limit = 10, sortField = null, sortDirection = 'asc') {
  try {
    // Get all business keys
    const keys = await client.keys('business:*');

    // Get all business records first (needed for sorting)
    const allBusinesses = [];
    for (const key of keys) {
      const businessId = key.split(':')[1];
      const business = await getBusinessRecord(client, businessId);

      if (business) {
        // If sorting by match_confidence, we need to get enrichment data
        if (sortField === 'match_confidence') {
          const enrichmentData = await getEnrichmentData(client, businessId);
          business.enrichment_data = enrichmentData;
        }
        allBusinesses.push(business);
      }
    }

    // Sort businesses (default to name if no sortField provided)
    const actualSortField = sortField || 'name';
    const actualSortDirection = sortField ? sortDirection : 'asc';

    allBusinesses.sort((a, b) => {
        let valA = a[actualSortField] || '';
        let valB = b[actualSortField] || '';

        // Handle different data types
        if (actualSortField === 'rating' || actualSortField === 'user_ratings_total') {
          valA = parseFloat(valA) || 0;
          valB = parseFloat(valB) || 0;
        } else if (actualSortField === 'touched' || actualSortField === 'created_at' || actualSortField === 'updated_at') {
          valA = valA ? new Date(valA).getTime() : 0;
          valB = valB ? new Date(valB).getTime() : 0;
        } else if (actualSortField === 'match_confidence') {
          // Extract highest match confidence from enrichment data
          valA = getHighestMatchConfidence(a);
          valB = getHighestMatchConfidence(b);
        } else {
          valA = valA.toString().toLowerCase();
          valB = valB.toString().toLowerCase();
        }

        if (valA < valB) return actualSortDirection === 'asc' ? -1 : 1;
        if (valA > valB) return actualSortDirection === 'asc' ? 1 : -1;
        return 0;
      });

    // Calculate pagination after sorting
    const totalPages = Math.ceil(allBusinesses.length / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;

    // Get paginated businesses
    const paginatedBusinesses = allBusinesses.slice(startIndex, endIndex);

    return {
      businesses: paginatedBusinesses,
      pagination: {
        total: allBusinesses.length,
        page,
        limit,
        pages: totalPages
      }
    };
  } catch (error) {
    logger.error('Error getting business records:', error);
    throw error;
  }
}

/**
 * Get the highest match confidence from a business's enrichment data
 * @param {Object} business - Business object
 * @returns {number} Highest match confidence (0-1)
 */
function getHighestMatchConfidence(business) {
  let highestConfidence = 0;

  if (business.enrichment_data && Object.keys(business.enrichment_data).length > 0) {
    Object.keys(business.enrichment_data).forEach(moduleName => {
      const moduleData = business.enrichment_data[moduleName];
      if (moduleData.match_confidence !== undefined) {
        if (moduleData.match_confidence > highestConfidence) {
          highestConfidence = moduleData.match_confidence;
        }
      }
    });
  }

  return highestConfidence;
}

/**
 * Parse JSON strings back to objects for enrichment data
 * @param {Object} data - Raw data from Redis
 * @returns {Object} Parsed data
 */
function parseEnrichmentData(data) {
  const parsedData = {};
  for (const [key, value] of Object.entries(data)) {
    try {
      // Try to parse as JSON, if it fails, keep as string
      parsedData[key] = JSON.parse(value);
    } catch (e) {
      // If parsing fails, it's probably a string value
      parsedData[key] = value;
    }
  }
  return parsedData;
}

/**
 * Get enrichment data for a business
 * @param {Object} client - Redis client
 * @param {string} businessId - Business ID
 * @param {string} module - Module name (optional)
 * @returns {Promise<Object>} Enrichment data
 */
async function getEnrichmentData(client, businessId, module = null) {
  try {
    if (module) {
      // Get enrichment data for a specific module
      const enrichmentKey = `connector:enrichment:${businessId}:${module}`;
      const enrichmentData = await client.hGetAll(enrichmentKey);

      if (Object.keys(enrichmentData).length > 0) {
        return parseEnrichmentData(enrichmentData);
      }
      return null;
    } else {
      // Get all enrichment data for the business
      const enrichmentKeys = await client.keys(`connector:enrichment:${businessId}:*`);

      if (enrichmentKeys.length === 0) {
        return {};
      }

      const enrichmentData = {};

      for (const key of enrichmentKeys) {
        const moduleName = key.split(':')[3];
        const moduleData = await client.hGetAll(key);

        if (Object.keys(moduleData).length > 0) {
          enrichmentData[moduleName] = parseEnrichmentData(moduleData);
        }
      }

      return enrichmentData;
    }
  } catch (error) {
    logger.error(`Error getting enrichment data for business ${businessId}:`, error);
    throw error;
  }
}

/**
 * Update a business record
 * @param {Object} client - Redis client
 * @param {string} businessId - Business ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<boolean>} Success status
 */
async function updateBusinessRecord(client, businessId, updates) {
  try {
    const businessKey = `business:${businessId}`;

    // Check if business exists
    const exists = await client.exists(businessKey);
    if (!exists) {
      throw new Error(`Business ${businessId} not found`);
    }

    // Update the business record
    await client.hSet(businessKey, updates);

    logger.info(`Updated business ${businessId} with fields: ${Object.keys(updates).join(', ')}`);
    return true;
  } catch (error) {
    logger.error(`Error updating business ${businessId}:`, error);
    throw error;
  }
}

/**
 * Save enrichment data for a business
 * @param {Object} client - Redis client
 * @param {string} businessId - Business ID
 * @param {string} module - Module name
 * @param {Object} data - Enrichment data
 * @returns {Promise<boolean>} Success status
 */
async function saveEnrichmentData(client, businessId, module, data) {
  try {
    const enrichmentKey = `connector:enrichment:${businessId}:${module}`;

    // Add timestamp to data
    data.timestamp = new Date().toISOString();

    // Convert objects to JSON strings for Redis storage
    const redisData = {};
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'object' && value !== null) {
        redisData[key] = JSON.stringify(value);
      } else {
        redisData[key] = value;
      }
    }

    // Save enrichment data
    await client.hSet(enrichmentKey, redisData);

    // Update the business record's touched timestamp
    const touchedTimestamp = new Date().toISOString();
    await updateBusinessRecord(client, businessId, {
      touched: touchedTimestamp,
      updated_at: touchedTimestamp
    });

    logger.info(`Saved enrichment data for business ${businessId} using module ${module}`);
    return true;
  } catch (error) {
    logger.error(`Error saving enrichment data for business ${businessId}:`, error);
    throw error;
  }
}

module.exports = {
  createRedisClient,
  getBusinessRecord,
  getBusinessRecords,
  updateBusinessRecord,
  getEnrichmentData,
  saveEnrichmentData
};
