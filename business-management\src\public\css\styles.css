/* Main styles for Business Management Dashboard */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f7fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.hidden {
  display: none !important;
}

/* Typography */
h1, h2, h3, h4 {
  margin-bottom: 15px;
  color: #2c3e50;
}

h1 {
  font-size: 28px;
}

h2 {
  font-size: 24px;
}

h3 {
  font-size: 20px;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 10px 15px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.btn:hover {
  background-color: #2980b9;
}

.btn:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3498db;
}

.btn-secondary {
  background-color: #95a5a6;
}

.btn-danger {
  background-color: #e74c3c;
}

.btn-danger:hover {
  background-color: #c0392b;
}

.btn-small {
  padding: 5px 10px;
  font-size: 14px;
}

/* Forms */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-group.checkbox {
  display: flex;
  align-items: center;
}

.form-group.checkbox input {
  width: auto;
  margin-right: 10px;
}

.form-group.checkbox label {
  margin-bottom: 0;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

/* Login page */
#login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.login-form {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 400px;
}

.login-form h1 {
  text-align: center;
  margin-bottom: 25px;
}

.login-info {
  margin-top: 20px;
  text-align: center;
  color: #7f8c8d;
  font-size: 14px;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 15px;
  text-align: center;
}

/* Dashboard layout */
header {
  background-color: #2c3e50;
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 {
  color: white;
  margin-bottom: 0;
  font-size: 24px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info span {
  margin-right: 15px;
}

.user-info button {
  margin-left: 10px;
}

main {
  display: flex;
  min-height: calc(100vh - 70px);
}

.sidebar {
  width: 300px;
  background-color: white;
  padding: 20px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  transition: margin-left 0.3s ease;
  position: relative;
}

.sidebar.collapsed {
  margin-left: -300px;
}

.sidebar-toggle {
  position: absolute;
  right: -30px;
  top: 20px;
  background: white;
  border: 1px solid #ddd;
  border-left: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 0 4px 4px 0;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
}

.sidebar-toggle i {
  transition: transform 0.3s ease;
}

.sidebar.collapsed .sidebar-toggle i {
  transform: rotate(180deg);
}

.content {
  flex: 1;
  padding: 20px;
  transition: margin-left 0.3s ease;
}

/* Businesses table */
.businesses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.businesses-table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

#businesses-table {
  width: 100%;
  border-collapse: collapse;
}

#businesses-table th,
#businesses-table td {
  padding: 8px 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

#businesses-table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

#businesses-table th.sortable {
  cursor: pointer;
  color: #2980b9;
  text-decoration: underline;
  transition: color 0.2s;
}

#businesses-table th.sortable:hover {
  color: #1a5a85;
}

#businesses-table tr:hover {
  background-color: #f5f7fa;
}

#loading-indicator,
#no-results {
  padding: 20px;
  text-align: center;
  color: #7f8c8d;
}

.pagination {
  display: flex;
  align-items: center;
}

.pagination button {
  margin: 0 10px;
}

.pagination .results-per-page {
  margin-left: 15px;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

/* Business detail */
.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.detail-header h2 {
  margin-left: 15px;
  margin-bottom: 0;
}

.detail-content {
  display: flex;
  gap: 20px;
}

.business-info,
.business-notes {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  padding: 20px;
  flex: 1;
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.note-item {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.note-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.note-meta {
  font-size: 14px;
  color: #7f8c8d;
}

.note-actions button {
  background: none;
  border: none;
  cursor: pointer;
  color: #3498db;
  margin-left: 10px;
}

.note-actions button:hover {
  color: #2980b9;
}

#no-notes {
  text-align: center;
  color: #7f8c8d;
  padding: 20px 0;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin-bottom: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #7f8c8d;
}

.modal form {
  padding: 20px;
}

/* Notification */
.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 15px 20px;
  background-color: #2ecc71;
  color: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: opacity 0.3s, transform 0.3s;
}

.notification.error {
  background-color: #e74c3c;
}

.notification.hidden {
  opacity: 0;
  transform: translateY(20px);
}

/* Icon links */
.icon-link {
  color: #3498db;
  font-size: 1.2em;
  margin: 0 5px;
}

.icon-link:hover {
  color: #2980b9;
}

.links-icons {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.icon-link.disabled {
  color: #e0e1e2 !important;
  pointer-events: none;
  cursor: default;
  opacity: 1;
}

/* Reviews range inputs */
.rating-range input[type="number"] {
  width: 100px;
  padding: 4px 8px;
  margin: 0 5px;
}

/* Responsive design */
@media (max-width: 992px) {
  main {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .detail-content {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .businesses-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .pagination {
    margin-top: 10px;
  }
  
  #businesses-table {
    display: block;
    overflow-x: auto;
  }
}
