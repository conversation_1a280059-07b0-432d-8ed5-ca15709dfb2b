const axios = require('axios');
const config = require('./config');
const { logger, logModuleError } = require('../../services/logService');

module.exports = {
  metadata: {
    name: 'Hunter Email Finder',
    description: 'Find email addresses associated with a business domain using Hunter.io API',
    version: '1.0.0',
    author: 'Business Connector Team'
  },
  
  /**
   * Validate Hunter API configuration
   * @returns {Promise<boolean>} True if API key is valid
   */
  validateConfig: async function() {
    try {
      const apiKey = config.getApiKey();
      
      if (!apiKey) {
        logger.warn('Hunter API key not configured');
        return false;
      }
      
      // Test API key with a simple request
      const response = await axios.get(`https://api.hunter.io/v2/account?api_key=${apiKey}`);
      
      if (response.status === 200) {
        logger.info('Hunter API key validated successfully');
        return true;
      } else {
        logModuleError('hunter', `API key validation failed with status ${response.status}`);
        return false;
      }
    } catch (error) {
      logModuleError('hunter', `API key validation error: ${error.message}`);
      return false;
    }
  },
  
  /**
   * Enrich a business record with email data from Hunter API
   * @param {Object} business - Business record to enrich
   * @returns {Promise<Object>} Enrichment data
   */
  enrich: async function(business) {
    try {
      if (!business.website) {
        return { error: 'Business has no website URL' };
      }
      
      const domain = extractDomain(business.website);
      const apiKey = config.getApiKey();
      
      if (!domain) {
        return { error: 'Could not extract domain from website URL' };
      }
      
      if (!apiKey) {
        return { error: 'Hunter API key not configured' };
      }
      
      logger.info(`Enriching business ${business.id} with Hunter API for domain ${domain}`);
      
      const response = await axios.get(`https://api.hunter.io/v2/domain-search?domain=${domain}&api_key=${apiKey}`);
      
      if (response.status !== 200) {
        throw new Error(`Hunter API returned status ${response.status}`);
      }
      
      const data = response.data.data;
      
      return {
        domain,
        emails: JSON.stringify(data.emails || []),
        organization: data.organization || '',
        country: data.country || '',
        state: data.state || '',
        city: data.city || '',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logModuleError('hunter', `Enrichment error: ${error.message}`, { businessId: business.id });
      return { error: error.message || 'Unknown error during enrichment' };
    }
  }
};

/**
 * Extract domain from a URL
 * @param {string} url - URL to extract domain from
 * @returns {string|null} Domain or null if extraction failed
 */
function extractDomain(url) {
  try {
    // Add protocol if missing
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    
    const domain = new URL(url).hostname;
    return domain.startsWith('www.') ? domain.substring(4) : domain;
  } catch (error) {
    logModuleError('hunter', `Domain extraction error: ${error.message}`, { url });
    return null;
  }
}
