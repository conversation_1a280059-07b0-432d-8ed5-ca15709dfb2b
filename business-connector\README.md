/**
 * Business Connector README
 * 
 * This file provides an overview of the Business Connector application,
 * its purpose, features, and links to more detailed documentation.
 */

# Business Connector

## Overview

The Business Connector is a modular application designed to enrich business records stored in a central Redis database. It provides a flexible, extensible framework for adding various enrichment modules while maintaining strict data integrity and compatibility with existing applications.

## Key Features

- **Modular Enrichment System**: Dynamically discover and load enrichment modules
- **Hunter API Integration**: Find email addresses associated with business domains
- **Redis Integration**: Seamless integration with existing Redis database
- **Namespaced Data Storage**: Ensures data integrity and compatibility
- **RESTful API**: Comprehensive API for business data and enrichment operations
- **Extensible Architecture**: Easy to add new enrichment modules

## Quick Start

1. Configure environment variables in `.env` file
2. Install dependencies: `npm install`
3. Start the application: `npm start`
4. Access the API at `http://localhost:3006/api`

For detailed installation instructions, see [INSTALLATION.md](INSTALLATION.md).

## API Endpoints

### Business Routes

- `GET /api/businesses` - Get all businesses with pagination
- `GET /api/businesses/:id` - Get a specific business by ID

### Enrichment Routes

- `GET /api/enrichment/modules` - Get all available enrichment modules
- `GET /api/enrichment/:businessId` - Get all enrichment data for a business
- `GET /api/enrichment/:businessId/:module` - Get specific module enrichment
- `POST /api/enrichment/:businessId/:module` - Run enrichment for a business
- `PUT /api/enrichment/:businessId/:module` - Update enrichment data

For detailed API documentation, see [DOCUMENTATION.md](DOCUMENTATION.md).

## Module Development

The Business Connector is designed to be easily extended with new enrichment modules. Each module must implement a standard interface and will be automatically discovered and loaded at runtime.

For module development guidelines, see [DOCUMENTATION.md](DOCUMENTATION.md#module-development).

## Integration with Existing Applications

The Business Connector is designed to work seamlessly with existing applications:

- Uses the same Redis instance
- Follows established key naming conventions
- Maintains compatibility with existing data structures

Other applications can access enrichment data by reading from the namespaced enrichment keys in Redis.

## Documentation

- [INSTALLATION.md](INSTALLATION.md) - Installation and setup instructions
- [DOCUMENTATION.md](DOCUMENTATION.md) - Comprehensive documentation

## License

MIT
