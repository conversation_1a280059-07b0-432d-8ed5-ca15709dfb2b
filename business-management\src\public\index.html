<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Management Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="/css/styles.css">
</head>
<body>
    <div id="app">
        <div id="login-container" class="container">
            <div class="login-form">
                <h1>Business Management Dashboard</h1>
                <div id="login-error" class="error-message"></div>
                <form id="login-form">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
                <p class="login-info">Default credentials: admin / admin123</p>
            </div>
        </div>

        <div id="dashboard-container" class="hidden">
            <header>
                <div class="logo">
                    <h1>Business Management Dashboard</h1>
                    <a href="http://localhost:3005/" target="_blank" class="header-link" style="margin-left:20px;font-size:15px;color:#fff;text-decoration:underline;">Go to Lead Gen</a>
                </div>
                <div class="user-info">
                    <span id="user-name"></span>
                    <button id="open-admin-settings" class="btn btn-small"><i class="fas fa-cog"></i> Settings</button>
                    <button id="logout-btn" class="btn btn-small">Logout</button>
                </div>
            </header>

            <main>
                <div class="sidebar">
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="search-container">
                        <h3>Search & Filter</h3>
                        <form id="search-form">
                            <div class="form-group">
                                <label for="search-keyword">Keyword</label>
                                <input type="text" id="search-keyword" name="keyword">
                            </div>
                            <div class="form-group">
                                <label for="search-country">Country</label>
                                <input type="text" id="search-country" name="country">
                            </div>
                            <div class="form-group">
                                <label for="search-city">City</label>
                                <input type="text" id="search-city" name="city">
                            </div>
                            <div class="form-group">
                                <label>Rating Range</label>
                                <div class="rating-range">
                                    <select id="search-min-rating" name="minRating">
                                        <option value="">No Min</option>
                                        <option value="1">1+</option>
                                        <option value="2">2+</option>
                                        <option value="3">3+</option>
                                        <option value="4">4+</option>
                                        <option value="4.5">4.5+</option>
                                    </select>
                                    <span>to</span>
                                    <select id="search-max-rating" name="maxRating">
                                        <option value="">No Max</option>
                                        <option value="1">1 or less</option>
                                        <option value="2">2 or less</option>
                                        <option value="3">3 or less</option>
                                        <option value="4">4 or less</option>
                                        <option value="4.5">4.5 or less</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Total Ratings Range</label>
                                <div class="rating-range">
                                    <input type="number" id="search-min-ratings" name="minRatings" placeholder="Min Ratings" min="0" step="1">
                                    <span>to</span>
                                    <input type="number" id="search-max-ratings" name="maxRatings" placeholder="Max Ratings" min="0" step="1">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Website Status</label>
                                <select id="search-website-status" name="websiteStatus">
                                    <option value="">Any</option>
                                    <option value="has">Has Website</option>
                                    <option value="no">No Website</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Phone Status</label>
                                <select id="search-phone-status" name="phoneStatus">
                                    <option value="">Any</option>
                                    <option value="has">Has Phone</option>
                                    <option value="no">No Phone</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Search</button>
                            <button type="reset" class="btn btn-secondary">Reset</button>
                        </form>
                    </div>
                </div>

                <div class="content">
                    <div class="businesses-container">
                        <div class="businesses-header">
                            <div class="pagination">
                                <button id="prev-page" class="btn btn-small" disabled>Previous</button>
                                <span id="page-info">Page 1 of 1</span>
                                <button id="next-page" class="btn btn-small" disabled>Next</button>
                                <select id="results-per-page" class="results-per-page" title="Results per page">
                                    <option value="5">5</option>
                                    <option value="10" selected>10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                </select>
                            </div>
                        </div>
                        <div class="businesses-table-container">
                            <table id="businesses-table">
                                <thead>
                                    <tr>
                                        <th class="sortable" data-sort="name">Name</th>
                                        <th class="sortable" data-sort="address">Address</th>
                                        <th class="sortable" data-sort="touched">Touched</th>
                                        <th>Links</th>
                                        <th class="sortable" data-sort="rating">Rating</th>
                                        <th class="sortable" data-sort="user_ratings_total">Ratings</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="businesses-list">
                                    <!-- Businesses will be loaded here -->
                                </tbody>
                            </table>
                            <div id="loading-indicator" class="hidden">Loading...</div>
                            <div id="no-results" class="hidden">No businesses found</div>
                        </div>
                    </div>

                    <div id="business-detail" class="hidden">
                        <div class="detail-header">
                            <button id="back-to-list" class="btn btn-small"><i class="fas fa-arrow-left"></i> Back to List</button>
                            <h2 id="detail-title">Business Details</h2>
                        </div>
                        <div class="detail-content">
                            <div class="business-info">
                                <h3>Business Information</h3>
                                <form id="edit-business-form">
                                    <input type="hidden" id="edit-business-id">
                                    <div class="form-group">
                                        <label for="edit-name">Name</label>
                                        <input type="text" id="edit-name" name="name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-address">Address</label>
                                        <input type="text" id="edit-address" name="address" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-phone">Phone</label>
                                        <input type="text" id="edit-phone" name="phone">
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-website">Website</label>
                                        <input type="text" id="edit-website" name="website">
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-rating">Rating</label>
                                        <input type="number" id="edit-rating" name="rating" min="0" max="5" step="0.1">
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-user_ratings_total">Number of Reviews</label>
                                        <input type="number" id="edit-user_ratings_total" name="user_ratings_total" min="0">
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-keyword">Keyword</label>
                                        <input type="text" id="edit-keyword" name="keyword">
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-country">Country</label>
                                        <input type="text" id="edit-country" name="country">
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-city">City</label>
                                        <input type="text" id="edit-city" name="city">
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-locality">Locality</label>
                                        <input type="text" id="edit-locality" name="locality">
                                    </div>
                                    <button type="submit" class="btn btn-primary">Save Changes</button>
                                </form>
                            </div>
                            <div class="business-notes">
                                <div class="notes-header">
                                    <h3>Notes</h3>
                                    <button id="add-note-btn" class="btn btn-small">Add Note</button>
                                </div>
                                <div id="notes-list">
                                    <!-- Notes will be loaded here -->
                                </div>
                                <div id="no-notes" class="hidden">No notes found</div>
                                <div id="add-note-form-container" class="hidden">
                                    <form id="add-note-form">
                                        <div class="form-group">
                                            <label for="note-content">Note</label>
                                            <textarea id="note-content" name="content" required></textarea>
                                        </div>
                                        <div class="form-actions">
                                            <button type="submit" class="btn btn-primary">Save Note</button>
                                            <button type="button" id="cancel-add-note" class="btn btn-secondary">Cancel</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <div id="notification" class="notification hidden"></div>
        </div>
    </div>

    <div id="edit-note-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Note</h3>
                <button id="close-edit-note-modal" class="close-btn">&times;</button>
            </div>
            <form id="edit-note-form">
                <input type="hidden" id="edit-note-id">
                <div class="form-group">
                    <label for="edit-note-content">Note</label>
                    <textarea id="edit-note-content" name="content" required></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                    <button type="button" id="delete-note-btn" class="btn btn-danger">Delete Note</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.4.1/socket.io.min.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/socket.js"></script>
    <script src="/js/businesses.js"></script>
    <script src="/js/notes.js"></script>
    <script src="/js/app.js"></script>
    <script>
        window.addEventListener('load', function() {
            if (initAuth()) {
                initializeApp();
            }
        });
    </script>
</body>
</html>
