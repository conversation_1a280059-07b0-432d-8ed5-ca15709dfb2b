const { createPlacesClient } = require('../config/places');
const validateInput = require('../utils/validation');
const { v4: uuidv4 } = require('uuid');

class PlacesService {
  constructor(redisService, io) {
    this.placesClient = createPlacesClient();
    this.redisService = redisService;
    this.io = io;
    this.activeSearches = new Map();
  }

  // Start a new search
  async startSearch(criteria) {
    // Validate search criteria
    const validation = validateInput.searchParams(criteria);
    if (!validation.isValid) {
      throw new Error(`Invalid search parameters: ${validation.errors.join(', ')}`);
    }

    // Create search ID and save search metadata
    const searchId = uuidv4();
    await this.redisService.saveSearch(searchId, criteria);
    console.log(`[WS DEBUG] Starting search with ID: ${searchId} and criteria:`, criteria);

    // Set default values if not provided
    const searchParams = {
      ...criteria,
      rateLimit: criteria.rateLimit || 5, // Default rate limit: 5 seconds
      maxResults: criteria.maxResults || 100 // Default max results: 100
    };

    // Create query string for Google Places API
    const query = validateInput.createQueryString(searchParams);

    // Start search process in background
    this.executeSearch(searchId, query, searchParams);

    return { searchId, status: 'in_progress' };
  }

  // Execute search in background
  async executeSearch(searchId, query, searchParams) {
    try {
      // Add to active searches
      this.activeSearches.set(searchId, { 
        cancelled: false,
        totalResults: 0
      });

      // Notify clients that search has started
      console.log(`[WS DEBUG] Emitting 'search:start' for searchId: ${searchId}`);
      this.io.emit('search:start', { 
        searchId, 
        criteria: searchParams 
      });

      let nextPageToken = null;
      let totalResults = 0;
      let currentPage = 1;
      let allResults = [];

      // Log the query being sent to Google Places API
      console.log('[DEBUG] Google Places API query:', query);
      // First page
      const initialResponse = await this.placesClient.textSearch({
        query,
        type: 'business'
      });
      // Log the raw response from Google Places API
      console.log('[DEBUG] Google Places API response:', initialResponse);

      // Process first page results
      if (initialResponse.results && initialResponse.results.length > 0) {
        await this.processResults(searchId, initialResponse.results, searchParams);
        totalResults += initialResponse.results.length;
        allResults = [...initialResponse.results];
        nextPageToken = initialResponse.next_page_token;
      }

      // Update search progress
      await this.redisService.updateSearchProgress(searchId, 
        Math.min(100, Math.round((totalResults / searchParams.maxResults) * 100)), 
        totalResults
      );

      // Notify clients of progress
      console.log(`[WS DEBUG] Emitting 'search:progress' for searchId: ${searchId}, progress: ${Math.min(100, Math.round((totalResults / searchParams.maxResults) * 100))}, totalResults: ${totalResults}, currentPage: ${currentPage}`);
      this.io.emit('search:progress', {
        searchId,
        progress: Math.min(100, Math.round((totalResults / searchParams.maxResults) * 100)),
        totalResults,
        currentPage
      });

      // Check if search was cancelled or max results reached
      const activeSearch = this.activeSearches.get(searchId);
      if (activeSearch.cancelled || totalResults >= searchParams.maxResults) {
        await this.completeSearch(searchId, totalResults);
        return;
      }

      // Process next pages if available
      while (nextPageToken && totalResults < searchParams.maxResults && !activeSearch.cancelled) {
        // Apply rate limiting
        await new Promise(resolve => setTimeout(resolve, searchParams.rateLimit * 1000));
        
        // Get next page
        const nextPageResponse = await this.placesClient.getNextPage(nextPageToken);
        currentPage++;
        
        if (nextPageResponse.results && nextPageResponse.results.length > 0) {
          await this.processResults(searchId, nextPageResponse.results, searchParams);
          totalResults += nextPageResponse.results.length;
          allResults = [...allResults, ...nextPageResponse.results];
          nextPageToken = nextPageResponse.next_page_token;
          
          // Update search progress
          await this.redisService.updateSearchProgress(searchId, 
            Math.min(100, Math.round((totalResults / searchParams.maxResults) * 100)), 
            totalResults
          );
          
          // Notify clients of progress
          console.log(`[WS DEBUG] Emitting 'search:progress' for searchId: ${searchId}, progress: ${Math.min(100, Math.round((totalResults / searchParams.maxResults) * 100))}, totalResults: ${totalResults}, currentPage: ${currentPage}`);
          this.io.emit('search:progress', {
            searchId,
            progress: Math.min(100, Math.round((totalResults / searchParams.maxResults) * 100)),
            totalResults,
            currentPage
          });
          
          // Check if max results reached
          if (totalResults >= searchParams.maxResults) {
            break;
          }
        } else {
          // No more results
          nextPageToken = null;
        }
      }

      // Complete search
      await this.completeSearch(searchId, totalResults);
    } catch (error) {
      console.error(`Error in search execution for ${searchId}:`, error);
      
      // Notify clients of error
      console.log(`[WS DEBUG] Emitting 'search:error' for searchId: ${searchId}, error: ${error.message}`);
      this.io.emit('search:error', {
        searchId,
        error: error.message
      });
      
      // Remove from active searches
      this.activeSearches.delete(searchId);
    }
  }

  // Process search results
  async processResults(searchId, results, searchParams) {
    const { minRating, maxRating, hasWebsite, noWebsite, hasPhone, noPhone, locality } = searchParams;
    
    for (const result of results) {
      // Check if search was cancelled
      const activeSearch = this.activeSearches.get(searchId);
      if (activeSearch.cancelled) {
        break;
      }
      
      try {
        // Get place details
        const detailsResponse = await this.placesClient.getDetails(result.place_id);
        const place = detailsResponse.result;
        
        // Skip if doesn't meet rating criteria
        if (minRating !== undefined && (!place.rating || place.rating < minRating)) {
          continue;
        }
        
        // Skip if exceeds max rating
        if (maxRating !== undefined && place.rating && place.rating > maxRating) {
          continue;
        }
        
        // Skip if website is required but not available
        if (hasWebsite && !place.website) {
          continue;
        }
        
        // Skip if noWebsite is required but website exists
        if (noWebsite && place.website) {
          continue;
        }

        // Skip if phone is required but not available
        if (hasPhone && !place.formatted_phone_number) {
          continue;
        }
        
        // Skip if noPhone is required but phone exists
        if (noPhone && place.formatted_phone_number) {
          continue;
        }
        
        // Create business object with stringified values
        const business = {
          places_id: result.place_id,
          name: place.name || result.name || '',
          address: place.formatted_address || result.formatted_address || '',
          phone: place.formatted_phone_number || '',
          website: place.website || '',
          rating: place.rating ? place.rating.toString() : '',
          user_ratings_total: place.user_ratings_total ? place.user_ratings_total.toString() : '0',
          keyword: searchParams.keyword || '',
          country: searchParams.country || '',
          city: searchParams.city || '',
          locality: locality || ''
        };
        
        // Save to Redis
        await this.redisService.saveBusiness(business);
        
        // Add to search results
        await this.redisService.addSearchResult(searchId, business.places_id);
        
        // Notify clients of new result
        console.log(`[WS DEBUG] Emitting 'search:result' for searchId: ${searchId}, business:`, business);
        this.io.emit('search:result', {
          searchId,
          business
        });
        
        // Apply rate limiting between detail requests
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`Error processing result ${result.place_id}:`, error);
        continue;
      }
    }
  }

  // Complete a search
  async completeSearch(searchId, totalResults) {
    await this.redisService.completeSearch(searchId, totalResults);
    
    // Notify clients that search is complete
    console.log(`[WS DEBUG] Emitting 'search:complete' for searchId: ${searchId}, totalResults: ${totalResults}`);
    this.io.emit('search:complete', {
      searchId,
      totalResults
    });
    
    // Remove from active searches
    this.activeSearches.delete(searchId);
  }

  // Cancel a search
  async cancelSearch(searchId) {
    const activeSearch = this.activeSearches.get(searchId);
    
    if (!activeSearch) {
      throw new Error(`Search ${searchId} not found or already completed`);
    }
    
    // Mark as cancelled
    activeSearch.cancelled = true;
    
    return { success: true, message: 'Search cancelled' };
  }

  // Get search results
  async getSearchResults(searchId) {
    const search = await this.redisService.getSearch(searchId);
    
    if (!search) {
      throw new Error(`Search ${searchId} not found`);
    }
    
    const results = await this.redisService.getSearchResults(searchId);
    
    return {
      results,
      filters: {
        keyword: search.criteria.keyword,
        country: search.criteria.country,
        city: search.criteria.city,
        locality: search.criteria.locality,
        minRating: search.criteria.minRating,
        hasWebsite: search.criteria.hasWebsite,
        noWebsite: search.criteria.noWebsite
      }
    };
  }

  // Get search status
  async getSearchStatus(searchId) {
    const search = await this.redisService.getSearch(searchId);
    
    if (!search) {
      throw new Error(`Search ${searchId} not found`);
    }
    
    return {
      searchId,
      status: search.status,
      progress: parseInt(search.progress),
      totalResults: parseInt(search.totalResults)
    };
  }
}

module.exports = PlacesService;
