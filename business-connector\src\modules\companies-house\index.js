/**
 * Companies House Enrichment Module
 *
 * This module enriches business data using the UK Companies House API.
 * It retrieves company profile, registered office address, persons with
 * significant control, and filing history.
 */

const axios = require('axios');
const config = require('./config');
const { logger, logModuleError } = require('../../services/logService');

/**
 * Extract company number from various formats
 * @param {string} input - Input string that might contain company number
 * @returns {string|null} Extracted company number or null
 */
function extractCompanyNumber(input) {
  if (!input) return null;

  // Remove common prefixes and clean up
  const cleaned = input.toString()
    .replace(/^(company\s+)?no\.?\s*/i, '')
    .replace(/^(reg\.?\s+)?no\.?\s*/i, '')
    .replace(/[^\w]/g, '')
    .toUpperCase();

  // UK company numbers are typically 8 digits, sometimes with leading zeros
  if (/^\d{6,8}$/.test(cleaned)) {
    return cleaned.padStart(8, '0');
  }

  return cleaned.length >= 6 ? cleaned : null;
}

/**
 * Create a simple address object from business address
 * @param {string} businessAddress - Business address string
 * @returns {Object} Simple address object
 */
function createAddressFromBusiness(businessAddress) {
  if (!businessAddress) {
    return {
      address_line_1: "Business Address Not Available",
      locality: "Unknown",
      postal_code: "",
      country: "United Kingdom"
    };
  }

  // For mock data, just use the full address as address_line_1
  // In real API calls, the full address would be sent to Companies House for matching
  return {
    address_line_1: businessAddress,
    locality: "",
    postal_code: "",
    country: "United Kingdom"
  };
}

/**
 * Generate mock search results for testing
 * @param {string} companyName - Name to search for
 * @param {string} businessAddress - Business address for mock data
 * @returns {Array} Mock search results
 */
function generateMockSearchResults(companyName, businessAddress = null) {
  const address = createAddressFromBusiness(businessAddress);

  return [
    {
      company_number: "12345678",
      title: companyName,
      company_status: "active",
      company_type: "ltd",
      date_of_creation: "2020-01-15",
      address: address,
      description: `${companyName} - Active company`
    }
  ];
}

/**
 * Search for companies by name
 * @param {string} companyName - Name to search for
 * @param {string} businessAddress - Business address for better matching
 * @param {number} limit - Maximum number of results
 * @returns {Promise<Array>} Array of company search results
 */
async function searchCompanies(companyName, businessAddress = null, limit = 5) {
  try {
    // Use mock data if no API key is configured
    if (config.isMockMode()) {
      logger.info(`Using mock data for Companies House search: ${companyName} at ${businessAddress || 'unknown address'}`);
      return generateMockSearchResults(companyName, businessAddress);
    }

    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();

    // Create search query that includes both company name and address for better matching
    let searchQuery = companyName;
    if (businessAddress) {
      searchQuery += ` ${businessAddress}`;
    }

    const response = await axios.get(`${baseUrl}/search/companies`, {
      headers,
      params: {
        q: searchQuery,
        items_per_page: limit
      }
    });

    return response.data.items || [];
  } catch (error) {
    logger.error(`Error searching companies: ${error.message}`);
    throw error;
  }
}

/**
 * Generate mock company profile
 * @param {string} companyNumber - Company number
 * @param {string} companyName - Company name to use in mock data
 * @param {string} businessAddress - Business address for mock data
 * @returns {Object} Mock company profile
 */
function generateMockCompanyProfile(companyNumber, companyName = "Example Company Ltd", businessAddress = null) {
  const address = createAddressFromBusiness(businessAddress);

  return {
    company_number: companyNumber,
    company_name: companyName,
    company_status: "active",
    company_type: "private-limited-company",
    date_of_creation: "2020-01-15",
    registered_office_address: address,
    sic_codes: ["86230"],
    accounts: {
      next_due: "2024-10-31",
      next_made_up_to: "2024-01-31"
    }
  };
}

/**
 * Get company profile by company number
 * @param {string} companyNumber - Company number
 * @param {string} companyName - Company name for mock data
 * @param {string} businessAddress - Business address for mock data
 * @returns {Promise<Object>} Company profile data
 */
async function getCompanyProfile(companyNumber, companyName = null, businessAddress = null) {
  try {
    // Use mock data if no API key is configured
    if (config.isMockMode()) {
      logger.info(`Using mock data for company profile: ${companyNumber}`);
      return generateMockCompanyProfile(companyNumber, companyName, businessAddress);
    }

    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();

    const response = await axios.get(`${baseUrl}/company/${companyNumber}`, {
      headers
    });

    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return null;
    }
    logger.error(`Error getting company profile: ${error.message}`);
    throw error;
  }
}

/**
 * Get registered office address
 * @param {string} companyNumber - Company number
 * @param {string} businessAddress - Business address for mock data
 * @returns {Promise<Object>} Registered office address data
 */
async function getRegisteredOfficeAddress(companyNumber, businessAddress = null) {
  try {
    // Use mock data if no API key is configured
    if (config.isMockMode()) {
      logger.info(`Using mock data for registered office address: ${companyNumber}`);
      return createAddressFromBusiness(businessAddress);
    }

    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();

    const response = await axios.get(`${baseUrl}/company/${companyNumber}/registered-office-address`, {
      headers
    });

    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return null;
    }
    logger.error(`Error getting registered office address: ${error.message}`);
    throw error;
  }
}

/**
 * Get persons with significant control
 * @param {string} companyNumber - Company number
 * @returns {Promise<Array>} Array of PSC data
 */
async function getPersonsWithSignificantControl(companyNumber) {
  try {
    // Use mock data if no API key is configured
    if (config.isMockMode()) {
      logger.info(`Using mock data for PSC: ${companyNumber}`);
      return [
        {
          name: "John Smith",
          kind: "individual-person-with-significant-control",
          natures_of_control: ["ownership-of-shares-75-to-100-percent"],
          nationality: "British",
          country_of_residence: "United Kingdom",
          date_of_birth: { month: 3, year: 1980 }
        }
      ];
    }

    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();

    const response = await axios.get(`${baseUrl}/company/${companyNumber}/persons-with-significant-control`, {
      headers
    });

    return response.data.items || [];
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return [];
    }
    logger.error(`Error getting PSC data: ${error.message}`);
    throw error;
  }
}

/**
 * Get filing history
 * @param {string} companyNumber - Company number
 * @param {number} limit - Maximum number of filings to retrieve
 * @returns {Promise<Array>} Array of filing history data
 */
async function getFilingHistory(companyNumber, limit = 10) {
  try {
    // Use mock data if no API key is configured
    if (config.isMockMode()) {
      logger.info(`Using mock data for filing history: ${companyNumber}`);
      return [
        {
          category: "accounts",
          description: "Annual accounts made up to 31 January 2024",
          date: "2024-02-15",
          type: "AA"
        },
        {
          category: "confirmation-statement",
          description: "Confirmation statement made on 15 January 2024",
          date: "2024-01-15",
          type: "CS01"
        }
      ];
    }

    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();

    const response = await axios.get(`${baseUrl}/company/${companyNumber}/filing-history`, {
      headers,
      params: {
        items_per_page: limit
      }
    });

    return response.data.items || [];
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return [];
    }
    logger.error(`Error getting filing history: ${error.message}`);
    throw error;
  }
}

module.exports = {
  metadata: {
    name: 'Companies House UK',
    description: 'Enriches business data using the UK Companies House API to retrieve company profiles, registered addresses, persons with significant control, and filing history',
    version: '1.0.0',
    author: 'Business Connector Team'
  },

  /**
   * Validate Companies House API configuration
   * @returns {Promise<boolean>} True if API key is valid
   */
  validateConfig: async function() {
    try {
      if (!config.validateConfig()) {
        return false;
      }

      // If in mock mode, skip API validation
      if (config.isMockMode()) {
        logger.info('Companies House module running in mock mode - validation skipped');
        return true;
      }

      // Test API key with a simple request
      const baseUrl = config.getBaseUrl();
      const headers = config.getHeaders();

      const response = await axios.get(`${baseUrl}/search/companies?q=test&items_per_page=1`, {
        headers,
        timeout: 10000
      });

      if (response.status === 200) {
        logger.info('Companies House API key validated successfully');
        return true;
      } else {
        logModuleError('companies-house', `API key validation failed with status ${response.status}`);
        return false;
      }
    } catch (error) {
      logModuleError('companies-house', `API key validation error: ${error.message}`);
      return false;
    }
  },

  /**
   * Enrich a business record with Companies House data
   * @param {Object} business - Business record to enrich
   * @returns {Promise<Object>} Enrichment data
   */
  enrich: async function(business) {
    try {
      if (!business.name) {
        return { error: 'Business has no name to search for' };
      }

      logger.info(`Enriching business ${business.id} with Companies House API for company "${business.name}" at "${business.address || 'unknown address'}"`);

      // Search for companies by name and address
      const searchResults = await searchCompanies(business.name, business.address);

      if (searchResults.length === 0) {
        return {
          search_results: [],
          message: 'No companies found matching the business name and address'
        };
      }

      // Get the best match (first result)
      const bestMatch = searchResults[0];
      const companyNumber = bestMatch.company_number;

      // Get detailed company information
      const [profile, registeredAddress, psc, filingHistory] = await Promise.allSettled([
        getCompanyProfile(companyNumber, business.name, business.address),
        getRegisteredOfficeAddress(companyNumber, business.address),
        getPersonsWithSignificantControl(companyNumber),
        getFilingHistory(companyNumber, 5)
      ]);

      const enrichmentData = {
        company_number: companyNumber,
        search_results: searchResults,
        company_profile: profile.status === 'fulfilled' ? profile.value : null,
        registered_office_address: registeredAddress.status === 'fulfilled' ? registeredAddress.value : null,
        persons_with_significant_control: psc.status === 'fulfilled' ? psc.value : null,
        filing_history: filingHistory.status === 'fulfilled' ? filingHistory.value : null,
        timestamp: new Date().toISOString(),
        enrichment_status: 'completed',
        enrichment_source: 'Companies House UK'
      };

      // Add any errors encountered
      const errors = [];
      if (profile.status === 'rejected') errors.push(`Profile: ${profile.reason.message}`);
      if (registeredAddress.status === 'rejected') errors.push(`Address: ${registeredAddress.reason.message}`);
      if (psc.status === 'rejected') errors.push(`PSC: ${psc.reason.message}`);
      if (filingHistory.status === 'rejected') errors.push(`Filing: ${filingHistory.reason.message}`);

      if (errors.length > 0) {
        enrichmentData.errors = errors;
        enrichmentData.enrichment_status = 'completed_with_errors';
      }

      return enrichmentData;
    } catch (error) {
      logModuleError('companies-house', `Enrichment error: ${error.message}`, { businessId: business.id });
      return { error: error.message || 'Unknown error during enrichment' };
    }
  }
};
