/**
 * Companies House Enrichment Module
 * 
 * This module enriches business data using the UK Companies House API.
 * It retrieves company profile, registered office address, persons with 
 * significant control, and filing history.
 */

const axios = require('axios');
const config = require('./config');
const { logger, logModuleError } = require('../../services/logService');

/**
 * Extract company number from various formats
 * @param {string} input - Input string that might contain company number
 * @returns {string|null} Extracted company number or null
 */
function extractCompanyNumber(input) {
  if (!input) return null;
  
  // Remove common prefixes and clean up
  const cleaned = input.toString()
    .replace(/^(company\s+)?no\.?\s*/i, '')
    .replace(/^(reg\.?\s+)?no\.?\s*/i, '')
    .replace(/[^\w]/g, '')
    .toUpperCase();
  
  // UK company numbers are typically 8 digits, sometimes with leading zeros
  if (/^\d{6,8}$/.test(cleaned)) {
    return cleaned.padStart(8, '0');
  }
  
  return cleaned.length >= 6 ? cleaned : null;
}

/**
 * Search for companies by name
 * @param {string} companyName - Name to search for
 * @param {number} limit - Maximum number of results
 * @returns {Promise<Array>} Array of company search results
 */
async function searchCompanies(companyName, limit = 5) {
  try {
    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();
    
    const response = await axios.get(`${baseUrl}/search/companies`, {
      headers,
      params: {
        q: companyName,
        items_per_page: limit
      }
    });
    
    return response.data.items || [];
  } catch (error) {
    logger.error(`Error searching companies: ${error.message}`);
    throw error;
  }
}

/**
 * Get company profile by company number
 * @param {string} companyNumber - Company number
 * @returns {Promise<Object>} Company profile data
 */
async function getCompanyProfile(companyNumber) {
  try {
    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();
    
    const response = await axios.get(`${baseUrl}/company/${companyNumber}`, {
      headers
    });
    
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return null;
    }
    logger.error(`Error getting company profile: ${error.message}`);
    throw error;
  }
}

/**
 * Get registered office address
 * @param {string} companyNumber - Company number
 * @returns {Promise<Object>} Registered office address data
 */
async function getRegisteredOfficeAddress(companyNumber) {
  try {
    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();
    
    const response = await axios.get(`${baseUrl}/company/${companyNumber}/registered-office-address`, {
      headers
    });
    
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return null;
    }
    logger.error(`Error getting registered office address: ${error.message}`);
    throw error;
  }
}

/**
 * Get persons with significant control
 * @param {string} companyNumber - Company number
 * @returns {Promise<Array>} Array of PSC data
 */
async function getPersonsWithSignificantControl(companyNumber) {
  try {
    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();
    
    const response = await axios.get(`${baseUrl}/company/${companyNumber}/persons-with-significant-control`, {
      headers
    });
    
    return response.data.items || [];
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return [];
    }
    logger.error(`Error getting PSC data: ${error.message}`);
    throw error;
  }
}

/**
 * Get filing history
 * @param {string} companyNumber - Company number
 * @param {number} limit - Maximum number of filings to retrieve
 * @returns {Promise<Array>} Array of filing history data
 */
async function getFilingHistory(companyNumber, limit = 10) {
  try {
    const baseUrl = config.getBaseUrl();
    const headers = config.getHeaders();
    
    const response = await axios.get(`${baseUrl}/company/${companyNumber}/filing-history`, {
      headers,
      params: {
        items_per_page: limit
      }
    });
    
    return response.data.items || [];
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return [];
    }
    logger.error(`Error getting filing history: ${error.message}`);
    throw error;
  }
}

module.exports = {
  metadata: {
    name: 'Companies House UK',
    description: 'Enriches business data using the UK Companies House API to retrieve company profiles, registered addresses, persons with significant control, and filing history',
    version: '1.0.0',
    author: 'Business Connector Team'
  },
  
  /**
   * Validate Companies House API configuration
   * @returns {Promise<boolean>} True if API key is valid
   */
  validateConfig: async function() {
    try {
      if (!config.validateConfig()) {
        return false;
      }
      
      // Test API key with a simple request
      const baseUrl = config.getBaseUrl();
      const headers = config.getHeaders();
      
      const response = await axios.get(`${baseUrl}/search/companies?q=test&items_per_page=1`, {
        headers,
        timeout: 10000
      });
      
      if (response.status === 200) {
        logger.info('Companies House API key validated successfully');
        return true;
      } else {
        logModuleError('companies-house', `API key validation failed with status ${response.status}`);
        return false;
      }
    } catch (error) {
      logModuleError('companies-house', `API key validation error: ${error.message}`);
      return false;
    }
  },
  
  /**
   * Enrich a business record with Companies House data
   * @param {Object} business - Business record to enrich
   * @returns {Promise<Object>} Enrichment data
   */
  enrich: async function(business) {
    try {
      if (!business.name) {
        return { error: 'Business has no name to search for' };
      }
      
      logger.info(`Enriching business ${business.id} with Companies House API for company "${business.name}"`);
      
      // Search for companies by name
      const searchResults = await searchCompanies(business.name);
      
      if (searchResults.length === 0) {
        return { 
          search_results: [],
          message: 'No companies found matching the business name'
        };
      }
      
      // Get the best match (first result)
      const bestMatch = searchResults[0];
      const companyNumber = bestMatch.company_number;
      
      // Get detailed company information
      const [profile, registeredAddress, psc, filingHistory] = await Promise.allSettled([
        getCompanyProfile(companyNumber),
        getRegisteredOfficeAddress(companyNumber),
        getPersonsWithSignificantControl(companyNumber),
        getFilingHistory(companyNumber, 5)
      ]);
      
      const enrichmentData = {
        company_number: companyNumber,
        search_results: JSON.stringify(searchResults),
        company_profile: profile.status === 'fulfilled' ? JSON.stringify(profile.value) : null,
        registered_office_address: registeredAddress.status === 'fulfilled' ? JSON.stringify(registeredAddress.value) : null,
        persons_with_significant_control: psc.status === 'fulfilled' ? JSON.stringify(psc.value) : null,
        filing_history: filingHistory.status === 'fulfilled' ? JSON.stringify(filingHistory.value) : null,
        timestamp: new Date().toISOString()
      };
      
      // Add any errors encountered
      const errors = [];
      if (profile.status === 'rejected') errors.push(`Profile: ${profile.reason.message}`);
      if (registeredAddress.status === 'rejected') errors.push(`Address: ${registeredAddress.reason.message}`);
      if (psc.status === 'rejected') errors.push(`PSC: ${psc.reason.message}`);
      if (filingHistory.status === 'rejected') errors.push(`Filing: ${filingHistory.reason.message}`);
      
      if (errors.length > 0) {
        enrichmentData.errors = JSON.stringify(errors);
      }
      
      return enrichmentData;
    } catch (error) {
      logModuleError('companies-house', `Enrichment error: ${error.message}`, { businessId: business.id });
      return { error: error.message || 'Unknown error during enrichment' };
    }
  }
};
