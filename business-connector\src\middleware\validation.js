/**
 * Validation middleware for request data
 */
const { logger } = require('../services/logService');

/**
 * Validate business ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateBusinessId(req, res, next) {
  const { businessId } = req.params;
  
  if (!businessId) {
    return res.status(400).json({
      success: false,
      error: 'Business ID is required'
    });
  }
  
  next();
}

/**
 * Validate module name
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateModuleName(req, res, next) {
  const { module } = req.params;
  
  if (!module) {
    return res.status(400).json({
      success: false,
      error: 'Module name is required'
    });
  }
  
  next();
}

/**
 * Validate enrichment data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateEnrichmentData(req, res, next) {
  const { data } = req.body;
  
  if (!data || typeof data !== 'object') {
    return res.status(400).json({
      success: false,
      error: 'Enrichment data is required and must be an object'
    });
  }
  
  next();
}

module.exports = {
  validateBusinessId,
  validateModuleName,
  validateEnrichmentData
};
