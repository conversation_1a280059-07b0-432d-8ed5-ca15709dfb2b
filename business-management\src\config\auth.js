const JwtStrategy = require('passport-jwt').Strategy;
const ExtractJwt = require('passport-jwt').ExtractJwt;
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'business-management-secret';

// Configure passport with JWT strategy
const configurePassport = (passport) => {
  const opts = {
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: JWT_SECRET
  };

  passport.use(
    new JwtStrategy(opts, async (jwt_payload, done) => {
      try {
        // In a real application, you would validate the user from a database
        // For this MVP, we'll just check if the user ID exists in the payload
        if (jwt_payload.id) {
          return done(null, jwt_payload);
        }
        return done(null, false);
      } catch (err) {
        console.error('Error in passport strategy:', err);
        return done(err, false);
      }
    })
  );
};

// Generate JWT token
const generateToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      username: user.username
    },
    JWT_SECRET,
    { expiresIn: '1d' }
  );
};

// Hash password
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
};

// Compare password
const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

module.exports = {
  configurePassport,
  generateToken,
  hashPassword,
  comparePassword,
  JWT_SECRET
};
