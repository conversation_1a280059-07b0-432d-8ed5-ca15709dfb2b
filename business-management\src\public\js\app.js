// Main application script

// DOM elements
const notificationElement = document.getElementById('notification');

// Initialize application
function initializeApp() {
  // Initialize socket connection
  initializeSocket();
  
  // Load businesses
  loadBusinesses(1, {});

  // Load admin settings modal HTML
  fetch('/admin-settings.html')
    .then(res => res.text())
    .then(html => {
      document.body.insertAdjacentHTML('beforeend', html);
      setupAdminSettings();
    });

  setupSidebarToggle();
}

// Show notification
function showNotification(message, isError = false) {
  notificationElement.textContent = message;
  notificationElement.classList.remove('hidden', 'error');
  
  if (isError) {
    notificationElement.classList.add('error');
  }
  
  // Hide after 3 seconds
  setTimeout(() => {
    notificationElement.classList.add('hidden');
  }, 3000);
}

// Default editable fields
let editableFields = ['name', 'address', 'phone', 'website', 'rating', 'user_ratings_total', 'keyword', 'country', 'city', 'locality'];

function setupAdminSettings() {
  const openBtn = document.getElementById('open-admin-settings');
  const modal = document.getElementById('admin-settings-modal');
  const closeBtn = document.getElementById('close-admin-settings-modal');
  const form = document.getElementById('admin-settings-form');

  // Load from localStorage if available
  const saved = localStorage.getItem('editableFields');
  if (saved) {
    editableFields = JSON.parse(saved);
    Array.from(form.elements['editableFields']).forEach(cb => {
      cb.checked = editableFields.includes(cb.value);
    });
  }

  openBtn.addEventListener('click', () => {
    modal.classList.remove('hidden');
  });
  closeBtn.addEventListener('click', () => {
    modal.classList.add('hidden');
  });
  form.addEventListener('submit', e => {
    e.preventDefault();
    editableFields = Array.from(form.elements['editableFields'])
      .filter(cb => cb.checked)
      .map(cb => cb.value);
    localStorage.setItem('editableFields', JSON.stringify(editableFields));
    modal.classList.add('hidden');
    updateEditBusinessFormFields();
  });
}

function updateEditBusinessFormFields() {
  const fields = ['name', 'address', 'phone', 'website', 'rating', 'user_ratings_total', 'keyword', 'country', 'city', 'locality'];
  fields.forEach(field => {
    const input = document.getElementById('edit-' + field);
    if (input) {
      input.disabled = !editableFields.includes(field);
    }
  });
}

function setupSidebarToggle() {
  const sidebar = document.querySelector('.sidebar');
  const toggleBtn = document.getElementById('sidebar-toggle');
  
  // Load saved state
  const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
  if (sidebarCollapsed) {
    sidebar.classList.add('collapsed');
  }

  toggleBtn.addEventListener('click', () => {
    sidebar.classList.toggle('collapsed');
    localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
  });
}

// Patch loadBusinessDetails to update field editability after loading
const origLoadBusinessDetails = loadBusinessDetails;
loadBusinessDetails = async function(businessId) {
  await origLoadBusinessDetails(businessId);
  updateEditBusinessFormFields();
};
