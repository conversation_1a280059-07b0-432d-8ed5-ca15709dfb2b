// Logs display and filtering
let logs = [];

/**
 * Initialize logs functionality
 */
function initLogs() {
    // Set up log filter
    const logFilter = document.getElementById('log-filter');
    logFilter.addEventListener('change', filterLogs);
    
    // Generate some sample logs for demonstration
    generateSampleLogs();
    
    // <PERSON>der logs
    renderLogs();
}

/**
 * Generate sample logs for demonstration
 */
function generateSampleLogs() {
    logs = [
        {
            timestamp: new Date().toISOString(),
            level: 'info',
            message: 'Application started'
        },
        {
            timestamp: new Date(Date.now() - 60000).toISOString(),
            level: 'info',
            message: 'Redis connection established'
        },
        {
            timestamp: new Date(Date.now() - 120000).toISOString(),
            level: 'info',
            message: 'Loaded module: Hunter Email Finder'
        },
        {
            timestamp: new Date(Date.now() - 180000).toISOString(),
            level: 'enrichment',
            message: 'Enrichment action: start | Business: 1 | Module: hunter'
        },
        {
            timestamp: new Date(Date.now() - 185000).toISOString(),
            level: 'enrichment',
            message: 'Enrichment action: complete | Business: 1 | Module: hunter | {"dataFields":["domain","emails","organization","country","state","city","timestamp"]}'
        },
        {
            timestamp: new Date(Date.now() - 300000).toISOString(),
            level: 'error',
            message: 'Error validating Hunter API key: Request failed with status code 401'
        },
        {
            timestamp: new Date(Date.now() - 360000).toISOString(),
            level: 'info',
            message: 'Hunter API key validated successfully'
        }
    ];
}

/**
 * Render logs in the log container
 */
function renderLogs() {
    const logsList = document.getElementById('logs-list');
    const logFilter = document.getElementById('log-filter');
    const filterValue = logFilter.value;
    
    // Filter logs
    const filteredLogs = filterValue === 'all' 
        ? logs 
        : logs.filter(log => log.level === filterValue);
    
    // Clear previous logs
    logsList.innerHTML = '';
    
    if (filteredLogs.length === 0) {
        logsList.innerHTML = '<div class="text-center">No logs found</div>';
        return;
    }
    
    // Sort logs by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Render logs
    filteredLogs.forEach(log => {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${log.level}`;
        
        // Format timestamp
        const timestamp = new Date(log.timestamp).toLocaleString();
        
        // Determine log icon
        let icon = 'info-circle';
        if (log.level === 'error') {
            icon = 'exclamation-circle';
        } else if (log.level === 'enrichment') {
            icon = 'sync';
        }
        
        logEntry.innerHTML = `
            <div class="log-timestamp">${timestamp}</div>
            <div><i class="fas fa-${icon} me-2"></i>${log.message}</div>
        `;
        
        logsList.appendChild(logEntry);
    });
}

/**
 * Filter logs based on selected filter
 */
function filterLogs() {
    renderLogs();
}
