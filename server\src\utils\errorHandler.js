// Error handling middleware
const errorHandler = (err, req, res, next) => {
  console.error(err.stack);
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';
  
  res.status(statusCode).json({
    success: false,
    error: message
  });
};

// Validation middleware for search requests
const validateSearchRequest = (req, res, next) => {
  const { keyword, country, city, locality, minRating, hasWebsite, noWebsite, rateLimit, maxResults } = req.body;
  
  // Validate required fields
  if (!keyword) {
    return res.status(400).json({
      success: false,
      error: 'Keyword is required'
    });
  }
  
  // Validate numeric fields
  if (minRating && (isNaN(minRating) || minRating < 0 || minRating > 5)) {
    return res.status(400).json({
      success: false,
      error: 'Min rating must be a number between 0 and 5'
    });
  }
  
  if (rateLimit && (isNaN(rateLimit) || rateLimit < 1)) {
    return res.status(400).json({
      success: false,
      error: 'Rate limit must be a positive number'
    });
  }
  
  if (maxResults && (isNaN(maxResults) || maxResults < 1)) {
    return res.status(400).json({
      success: false,
      error: 'Max results must be a positive number'
    });
  }
  
  // No validation needed for locality or noWebsite
  next();
};

module.exports = {
  errorHandler,
  validateSearchRequest
};
