const express = require('express');
const router = express.Router();
const passport = require('passport');
const { generateToken, comparePassword, hashPassword } = require('../config/auth');

// Mock user database for MVP
// In a production app, this would be stored in a database
const users = [
  {
    id: '1',
    username: 'admin',
    // Default password: admin123
    password: '$2a$10$XgNuWtWXTLH.kCt0KVEh8.X6HQkJX/QeG0aDwvUQ.WwQzWEsQMOTi'
  }
];

// @route   POST api/auth/login
// @desc    Login user and return JWT token
// @access  Public
router.post('/login', async (req, res) => {
  // TEMPORARY: Development bypass for testing
  console.log('DEV MODE: Bypassing authentication');
  const devUser = {
    id: '1',
    username: 'admin'
  };
  const token = generateToken(devUser);
  return res.json({
    success: true,
    token: `Bearer ${token}`,
    user: devUser
  });

  // Regular authentication code (temporarily bypassed)
  const { username, password } = req.body;
  console.log('Login attempt:', { username }); // Don't log passwords!

  // Validate input
  if (!username || !password) {
    console.log('Missing username or password');
    return res.status(400).json({ message: 'Please provide username and password' });
  }

  try {
    // Find user by username
    const user = users.find(user => user.username === username);
    console.log('User found:', !!user);

    // Check if user exists
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check password
    const isMatch = await comparePassword(password, user.password);
    console.log('Password match:', isMatch);
    
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Create JWT token
    const token = generateToken(user);

    // Return token and user info
    res.json({
      success: true,
      token: `Bearer ${token}`,
      user: {
        id: user.id,
        username: user.username
      }
    });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', async (req, res) => {
  const { username, password } = req.body;

  // Validate input
  if (!username || !password) {
    return res.status(400).json({ message: 'Please provide username and password' });
  }

  try {
    // Check if user already exists
    const existingUser = users.find(user => user.username === username);
    if (existingUser) {
      return res.status(400).json({ message: 'Username already exists' });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create new user
    const newUser = {
      id: (users.length + 1).toString(),
      username,
      password: hashedPassword
    };

    // Add user to mock database
    users.push(newUser);

    // Create JWT token
    const token = generateToken(newUser);

    // Return token and user info
    res.json({
      success: true,
      token: `Bearer ${token}`,
      user: {
        id: newUser.id,
        username: newUser.username
      }
    });
  } catch (err) {
    console.error('Registration error:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET api/auth/user
// @desc    Get current user
// @access  Private
router.get('/user', passport.authenticate('jwt', { session: false }), (req, res) => {
  res.json({
    id: req.user.id,
    username: req.user.username
  });
});

module.exports = router;
