/**
 * Business Connector Documentation
 * 
 * This file provides comprehensive documentation for the Business Connector
 * application, including setup instructions, API endpoints, and module development.
 */

# Business Connector

## Overview

The Business Connector is a modular application designed to enrich business records stored in a central Redis database. It provides a flexible, extensible framework for adding various enrichment modules while maintaining strict data integrity and compatibility with existing applications.

## Installation

### Prerequisites

- Node.js 14+
- Redis server
- Docker (optional, for containerized deployment)

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
PORT=3006
REDIS_URL=redis://localhost:6379
HUNTER_API_KEY=your_hunter_api_key
NODE_ENV=production
```

### Installation Steps

1. Clone the repository
2. Install dependencies: `npm install`
3. Create `.env` file with required variables
4. Start the application: `npm start`

### Docker Deployment

To deploy using Docker:

1. Build the Docker image: `docker build -t business-connector .`
2. Run the container: `docker run -p 3006:3006 --env-file .env business-connector`

## API Endpoints

### Business Routes

- `GET /api/businesses` - Get all businesses with pagination and filtering
  - Query parameters:
    - `page` (default: 1) - Page number
    - `limit` (default: 10) - Number of records per page
  - Response: List of businesses with enrichment status

- `GET /api/businesses/:id` - Get a specific business by ID
  - Response: Business details with enrichment data

### Enrichment Routes

- `GET /api/enrichment/modules` - Get all available enrichment modules
  - Response: List of modules with metadata and configuration status

- `GET /api/enrichment/:businessId` - Get all enrichment data for a business
  - Response: All enrichment data for the specified business

- `GET /api/enrichment/:businessId/:module` - Get specific module enrichment for a business
  - Response: Enrichment data for the specified module and business

- `POST /api/enrichment/:businessId/:module` - Run enrichment for a business using a specific module
  - Response: Enrichment results

- `PUT /api/enrichment/:businessId/:module` - Update enrichment data for a business
  - Request body: `{ data: { ... } }` - Enrichment data to save
  - Response: Success message

## Module Development

### Module Structure

Modules are stored in the `src/modules` directory, with each module in its own subdirectory:

```
modules/
  ├── moduleInterface.js
  ├── index.js
  └── module-name/
      ├── index.js
      └── config.js
```

### Module Interface

Each module must implement the following interface:

```javascript
module.exports = {
  metadata: {
    name: 'Module Name',
    description: 'Module Description',
    version: '1.0.0',
    author: 'Author Name'
  },
  
  validateConfig: async function() {
    // Validate module configuration
    return true;
  },
  
  enrich: async function(business) {
    // Enrich business data
    return { /* enrichment data */ };
  }
};
```

### Creating a New Module

1. Create a new directory in `src/modules`
2. Create an `index.js` file implementing the module interface
3. Create a `config.js` file for module-specific configuration
4. The module will be automatically discovered and loaded at runtime

## Redis Key Structure

The Business Connector uses a namespaced key structure to ensure data integrity:

- **Read-only access**: `business:{id}` - Core business data
- **Write access**: `connector:enrichment:{business_id}:{module}` - Enrichment data

## Logging

Logs are stored in the following files:

- `combined.log` - All logs
- `error.log` - Error logs only

Log levels can be configured using the `LOG_LEVEL` environment variable.

## Troubleshooting

### Common Issues

- **Module not loading**: Ensure the module implements the required interface
- **API key validation failing**: Check environment variables and API key validity
- **Redis connection errors**: Verify Redis URL and server status

### Validation

Run the validation script to test core functionality:

```
node src/tests/validate.js
```

## Integration with Existing Applications

The Business Connector is designed to work seamlessly with existing applications:

- Uses the same Redis instance
- Follows established key naming conventions
- Maintains compatibility with existing data structures

Other applications can access enrichment data by reading from the namespaced enrichment keys in Redis.
