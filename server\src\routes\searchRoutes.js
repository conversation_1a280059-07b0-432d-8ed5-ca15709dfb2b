const express = require('express');
const { validateSearchRequest } = require('../utils/errorHandler');

function createSearchRoutes(placesService) {
  const router = express.Router();

  // Start a new search
  router.post('/', validateSearchRequest, async (req, res, next) => {
    try {
      const { keyword, country, city, locality, minRating, hasWebsite, noWebsite, rateLimit, maxResults } = req.body;
      
      const result = await placesService.startSearch({
        keyword,
        country,
        city,
        locality, // Pass locality
        minRating: minRating ? parseFloat(minRating) : undefined,
        hasWebsite: hasWebsite === true || hasWebsite === 'true',
        noWebsite: noWebsite === true || noWebsite === 'true', // Pass noWebsite
        rateLimit: rateLimit ? parseInt(rateLimit) : undefined,
        maxResults: maxResults ? parseInt(maxResults) : undefined
      });
      
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  });

  // Get search status
  router.get('/:searchId', async (req, res, next) => {
    try {
      const { searchId } = req.params;
      const result = await placesService.getSearchStatus(searchId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  });

  // Get search results
  router.get('/:searchId/results', async (req, res, next) => {
    try {
      const { searchId } = req.params;
      const result = await placesService.getSearchResults(searchId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  });

  // Cancel a search
  router.post('/cancel/:searchId', async (req, res, next) => {
    try {
      const { searchId } = req.params;
      const result = await placesService.cancelSearch(searchId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  });

  return router;
}

module.exports = createSearchRoutes;
