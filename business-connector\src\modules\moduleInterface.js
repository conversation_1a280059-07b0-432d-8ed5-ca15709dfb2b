/**
 * Module Interface Contract
 * 
 * This file defines the interface that all enrichment modules must implement.
 * Modules are dynamically discovered and loaded at runtime.
 */

module.exports = {
  /**
   * Module metadata
   * Required properties:
   * - name: Human-readable name of the module
   * - description: Brief description of what the module does
   * - version: Semantic version of the module
   * - author: Name of the module author
   */
  metadata: {
    name: 'Example Module',
    description: 'Example module description',
    version: '1.0.0',
    author: 'Example Author'
  },
  
  /**
   * Validate module configuration
   * This method should check if the module is properly configured,
   * including validating API keys and other required settings.
   * 
   * @returns {Promise<boolean>} True if module is properly configured
   */
  validateConfig: async function() {
    return true;
  },
  
  /**
   * Enrich a business record
   * This method should use the business data to perform enrichment
   * and return the enriched data.
   * 
   * @param {Object} business - Business record to enrich
   * @returns {Promise<Object>} Enrichment data
   */
  enrich: async function(business) {
    return {};
  }
};
