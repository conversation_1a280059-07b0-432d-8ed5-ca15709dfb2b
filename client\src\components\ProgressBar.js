import React from 'react';
import './ProgressBar.css';

const ProgressBar = ({ progress, status, onCancel }) => {
  return (
    <div className="progress-container">
      <div className="progress-header">
        <h3>Search Progress</h3>
        {status === 'in_progress' && (
          <button className="cancel-button" onClick={onCancel}>
            Cancel
          </button>
        )}
      </div>
      
      <div className="progress-bar-container">
        <div 
          className="progress-bar" 
          style={{ width: `${progress}%` }}
        ></div>
      </div>
      
      <div className="progress-info">
        <span className="progress-percentage">{progress}%</span>
        <span className="progress-status">
          {status === 'in_progress' && 'Searching...'}
          {status === 'completed' && 'Search completed'}
          {status === 'cancelled' && 'Search cancelled'}
          {status === 'error' && 'Error occurred'}
        </span>
      </div>
    </div>
  );
};

export default ProgressBar;
