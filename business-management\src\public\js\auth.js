// Authentication related functions

// Store for auth token
let authToken = localStorage.getItem('token');
let currentUser = null;

// DOM elements
const loginContainer = document.getElementById('login-container');
const dashboardContainer = document.getElementById('dashboard-container');
const loginForm = document.getElementById('login-form');
const loginError = document.getElementById('login-error');
const userNameElement = document.getElementById('user-name');
const logoutBtn = document.getElementById('logout-btn');

// Login function
async function login(username, password) {
  try {
    const response = await fetch(`/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Login failed');
    }

    // Store token and user info
    authToken = data.token;
    currentUser = data.user;
    
    // Save to localStorage
    localStorage.setItem('token', authToken);
    localStorage.setItem('user', JSON.stringify(currentUser));
    
    return true;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
}

// Logout function
function logout() {
  // Clear auth data
  authToken = null;
  currentUser = null;
  
  // Remove from localStorage
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  
  // Disconnect socket
  if (window.socket) {
    window.socket.disconnect();
  }
  
  // Show login screen
  showLoginScreen();
}

// Check if user is authenticated
function isAuthenticated() {
  return !!authToken;
}

// Get auth token
function getAuthToken() {
  return authToken;
}

// Get current user
function getCurrentUser() {
  return currentUser;
}

// Show login screen
function showLoginScreen() {
  loginContainer.classList.remove('hidden');
  dashboardContainer.classList.add('hidden');
  loginError.textContent = '';
}

// Show dashboard
function showDashboard() {
  loginContainer.classList.add('hidden');
  dashboardContainer.classList.remove('hidden');
  
  // Update user info
  if (currentUser) {
    userNameElement.textContent = currentUser.username;
  }
}

// Initialize auth
function initAuth() {
  // Try to get user from localStorage
  const storedUser = localStorage.getItem('user');
  if (storedUser) {
    try {
      currentUser = JSON.parse(storedUser);
    } catch (e) {
      console.error('Error parsing stored user:', e);
    }
  }
  
  // Check if we have a token
  if (isAuthenticated()) {
    showDashboard();
    return true;
  } else {
    showLoginScreen();
    return false;
  }
}

// Event listeners
loginForm.addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;
  
  loginError.textContent = '';
  
  try {
    await login(username, password);
    showDashboard();
    
    // Initialize app after login
    initializeApp();
  } catch (error) {
    loginError.textContent = error.message || 'Login failed. Please try again.';
  }
});

logoutBtn.addEventListener('click', () => {
  logout();
});

// Attach functions to window for global access
window.initAuth = initAuth;
window.login = login;
window.logout = logout;
window.getAuthToken = getAuthToken;
window.getCurrentUser = getCurrentUser;
