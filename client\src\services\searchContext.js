import React, { createContext, useContext, useState, useEffect } from 'react';
import api from './api';
import { useSocket } from './socket';

const SearchContext = createContext();

export const useSearch = () => useContext(SearchContext);

export const SearchProvider = ({ children }) => {
  const [currentSearch, setCurrentSearch] = useState(null);
  const [searchStatus, setSearchStatus] = useState(null);
  const [searchProgress, setSearchProgress] = useState(0);
  const [searchResults, setSearchResults] = useState([]);
  const [searchError, setSearchError] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const { socket, connected } = useSocket();

  // Listen for socket events
  useEffect(() => {
    if (!socket) return;

    // Debug: log all socket events
    socket.onAny((event, ...args) => {
      console.log(`[WS DEBUG] Received event: ${event}`, ...args);
    });

    // Search started
    socket.on('search:start', (data) => {
      console.log('Search started:', data);
      setCurrentSearch(data);
      setSearchStatus('in_progress');
      setSearchResults([]);
      setSearchError(null);
    });

    // Search progress
    socket.on('search:progress', (data) => {
      console.log('Search progress:', data);
      setSearchProgress(data.progress);
      setSearchStatus('in_progress');
    });

    // New search result
    socket.on('search:result', (data) => {
      console.log('New result:', data);
      setSearchResults((prevResults) => {
        // Check if result already exists
        const exists = prevResults.some(
          (result) => result.places_id === data.business.places_id
        );
        
        if (exists) {
          return prevResults;
        }
        
        return [...prevResults, data.business];
      });
    });

    // Search completed
    socket.on('search:complete', (data) => {
      console.log('Search completed:', data);
      setSearchStatus('completed');
      setSearchProgress(100);
      setIsSearching(false);
    });

    // Search error
    socket.on('search:error', (data) => {
      console.error('Search error:', data);
      setSearchError(data.error);
      setSearchStatus('error');
      setIsSearching(false);
    });

    return () => {
      socket.off('search:start');
      socket.off('search:progress');
      socket.off('search:result');
      socket.off('search:complete');
      socket.off('search:error');
      socket.offAny();
    };
  }, [socket]);

  // Start a new search
  const startSearch = async (searchParams) => {
    try {
      setIsSearching(true);
      setSearchError(null);
      setSearchResults([]);
      setSearchProgress(0);
      
      const response = await api.startSearch(searchParams);
      setCurrentSearch({
        searchId: response.searchId,
        criteria: searchParams
      });
      
      return response;
    } catch (error) {
      setSearchError(error.message || 'Failed to start search');
      setIsSearching(false);
      throw error;
    }
  };

  // Cancel current search
  const cancelSearch = async () => {
    if (!currentSearch) return;
    
    try {
      await api.cancelSearch(currentSearch.searchId);
      setIsSearching(false);
      setSearchStatus('cancelled');
    } catch (error) {
      setSearchError(error.message || 'Failed to cancel search');
    }
  };

  // Get search results
  const getResults = async (searchId) => {
    try {
      const response = await api.getSearchResults(searchId || currentSearch?.searchId);
      setSearchResults(response.results);
      return response;
    } catch (error) {
      setSearchError(error.message || 'Failed to get search results');
      throw error;
    }
  };

  return (
    <SearchContext.Provider
      value={{
        currentSearch,
        searchStatus,
        searchProgress,
        searchResults,
        searchError,
        isSearching,
        startSearch,
        cancelSearch,
        getResults
      }}
    >
      {children}
    </SearchContext.Provider>
  );
};
