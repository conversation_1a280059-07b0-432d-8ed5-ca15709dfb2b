{"name": "business-connector", "version": "1.0.0", "description": "Business Connector for enriching business data in Redis", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "redis": "^4.6.10", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}}