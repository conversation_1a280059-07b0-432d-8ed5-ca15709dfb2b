import React from 'react';
import './Dashboard.css';
import SearchForm from './SearchForm';
import ProgressBar from './ProgressBar';
import FilterPanel from './FilterPanel';
import ResultsList from './ResultsList';
import { useSearch } from '../services/searchContext';
import { useState, useEffect } from 'react';

const Dashboard = () => {
  const { 
    currentSearch,
    searchStatus,
    searchProgress,
    searchResults,
    searchError,
    isSearching,
    startSearch,
    cancelSearch
  } = useSearch();

  const [filteredResults, setFilteredResults] = useState([]);
  const [filters, setFilters] = useState({
    minRating: 0,
    country: '',
    city: '',
    locality: '', // Add locality
    hasWebsite: false,
    noWebsite: false // Add noWebsite
  });

  // Apply filters to search results
  useEffect(() => {
    if (!searchResults || searchResults.length === 0) {
      setFilteredResults([]);
      return;
    }

    let results = [...searchResults];

    // Filter by minimum rating
    if (filters.minRating > 0) {
      results = results.filter(
        business => business.rating && parseFloat(business.rating) >= filters.minRating
      );
    }

    // Filter by country
    if (filters.country) {
      const countryLower = filters.country.toLowerCase();
      results = results.filter(
        business => business.country && business.country.toLowerCase().includes(countryLower)
      );
    }

    // Filter by city
    if (filters.city) {
      const cityLower = filters.city.toLowerCase();
      results = results.filter(
        business => business.city && business.city.toLowerCase().includes(cityLower)
      );
    }

    // Filter by locality
    if (filters.locality) {
      const localityLower = filters.locality.toLowerCase();
      results = results.filter(
        business => business.address && business.address.toLowerCase().includes(localityLower)
      );
    }

    // Filter by website availability
    if (filters.hasWebsite && !filters.noWebsite) {
      results = results.filter(business => business.website);
    } else if (!filters.hasWebsite && filters.noWebsite) {
      results = results.filter(business => !business.website);
    }

    setFilteredResults(results);
  }, [searchResults, filters]);

  // Handle search form submission
  const handleSearch = (searchParams) => {
    startSearch(searchParams);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  return (
    <div className="dashboard">
      <SearchForm onSubmit={handleSearch} isSearching={isSearching} />
      
      {isSearching && (
        <ProgressBar 
          progress={searchProgress} 
          status={searchStatus} 
          onCancel={cancelSearch} 
        />
      )}
      
      {searchError && (
        <div className="error-message">
          <p>Error: {searchError}</p>
        </div>
      )}
      
      {searchResults && searchResults.length > 0 && (
        <>
          <FilterPanel 
            filters={currentSearch?.criteria} 
            onFilterChange={handleFilterChange} 
          />
          
          <ResultsList 
            results={filteredResults} 
            isLoading={isSearching && filteredResults.length === 0} 
          />
        </>
      )}
    </div>
  );
};

export default Dashboard;
