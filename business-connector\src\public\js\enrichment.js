// Enrichment functionality
let currentBusinessId = null;
let currentBusiness = null;
let currentEnrichmentData = null;

/**
 * Setup enrichment tab in business detail modal
 * @param {string} businessId - Business ID
 * @param {Object} business - Business object
 * @param {Object} enrichmentData - Existing enrichment data
 */
function setupEnrichmentTab(businessId, business, enrichmentData) {
    currentBusinessId = businessId;
    currentBusiness = business;
    currentEnrichmentData = enrichmentData || {};

    // Set up event listeners
    setupEnrichmentEventListeners();

    // Display existing enrichment data
    displayEnrichmentData();
}

/**
 * Setup event listeners for enrichment functionality
 */
function setupEnrichmentEventListeners() {
    const runEnrichmentBtn = document.getElementById('run-enrichment-btn');
    const saveEnrichmentBtn = document.getElementById('save-enrichment-btn');
    const moduleSelect = document.getElementById('module-select');

    if (runEnrichmentBtn) {
        // Remove existing listeners to prevent duplicates
        runEnrichmentBtn.replaceWith(runEnrichmentBtn.cloneNode(true));
        const newRunBtn = document.getElementById('run-enrichment-btn');

        newRunBtn.addEventListener('click', async () => {
            const selectedModule = moduleSelect.value;
            if (!selectedModule) {
                alert('Please select a module first.');
                return;
            }
            await runBusinessEnrichment(selectedModule);
        });
    }

    if (saveEnrichmentBtn) {
        // Remove existing listeners to prevent duplicates
        saveEnrichmentBtn.replaceWith(saveEnrichmentBtn.cloneNode(true));
        const newSaveBtn = document.getElementById('save-enrichment-btn');

        newSaveBtn.addEventListener('click', async () => {
            const selectedModule = moduleSelect.value;
            if (!selectedModule) {
                alert('Please select a module first.');
                return;
            }
            await saveCurrentEnrichmentData(selectedModule);
        });
    }
}

/**
 * Display existing enrichment data
 */
function displayEnrichmentData() {
    const outputContainer = document.getElementById('enrichment-output-container');

    if (!outputContainer) {
        return;
    }

    if (!currentEnrichmentData || Object.keys(currentEnrichmentData).length === 0) {
        outputContainer.innerHTML = '<div class="text-muted">No enrichment data available. Select a module and run enrichment to get started.</div>';
        return;
    }

    let html = '<h6>Existing Enrichment Data:</h6>';

    Object.keys(currentEnrichmentData).forEach(moduleName => {
        const moduleData = currentEnrichmentData[moduleName];
        html += `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-puzzle-piece me-2"></i>
                        ${moduleName}
                    </h6>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-2 rounded small">${JSON.stringify(moduleData, null, 2)}</pre>
                </div>
            </div>
        `;
    });

    outputContainer.innerHTML = html;
}

/**
 * Run enrichment for the current business
 * @param {string} moduleName - Name of the module to use
 */
async function runBusinessEnrichment(moduleName) {
    try {
        const runBtn = document.getElementById('run-enrichment-btn');
        const saveBtn = document.getElementById('save-enrichment-btn');
        const outputContainer = document.getElementById('enrichment-output-container');

        // Show loading state
        runBtn.disabled = true;
        runBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Running...';

        outputContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Running enrichment...</div>';

        // Run enrichment
        const enrichmentResult = await runEnrichment(currentBusinessId, moduleName);

        // Display results
        displayEnrichmentResult(moduleName, enrichmentResult);

        // Show save button
        saveBtn.classList.remove('d-none');

        // Reset run button
        runBtn.disabled = false;
        runBtn.innerHTML = '<i class="fas fa-play me-1"></i> Run Enrichment';

    } catch (error) {
        console.error('Error running enrichment:', error);

        const outputContainer = document.getElementById('enrichment-output-container');
        outputContainer.innerHTML = `<div class="alert alert-danger">Error running enrichment: ${error.message}</div>`;

        // Reset run button
        const runBtn = document.getElementById('run-enrichment-btn');
        runBtn.disabled = false;
        runBtn.innerHTML = '<i class="fas fa-play me-1"></i> Run Enrichment';
    }
}

/**
 * Display enrichment result
 * @param {string} moduleName - Name of the module
 * @param {Object} result - Enrichment result
 */
function displayEnrichmentResult(moduleName, result) {
    const outputContainer = document.getElementById('enrichment-output-container');

    // Check if this is Companies House data and format it specially
    if (moduleName === 'companies-house' || (result.enrichment_source && result.enrichment_source.includes('Companies House'))) {
        outputContainer.innerHTML = formatCompaniesHouseData(result);
    } else {
        // Default formatting for other modules
        let html = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                Enrichment completed successfully with module: ${moduleName}
            </div>
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Enrichment Results</h6>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded">${JSON.stringify(result, null, 2)}</pre>
                </div>
            </div>
        `;
        outputContainer.innerHTML = html;
    }

    // Store the result for saving
    if (!currentEnrichmentData) {
        currentEnrichmentData = {};
    }
    currentEnrichmentData[moduleName] = result;
}

/**
 * Format Companies House data for display
 * @param {Object} data - Companies House enrichment data
 * @returns {string} Formatted HTML
 */
function formatCompaniesHouseData(data) {
    const status = data.enrichment_status || 'completed';
    const statusClass = status === 'completed' ? 'success' : status === 'completed_with_errors' ? 'warning' : 'info';
    const statusIcon = status === 'completed' ? 'check-circle' : status === 'completed_with_errors' ? 'exclamation-triangle' : 'info-circle';

    let html = `
        <div class="alert alert-${statusClass}">
            <i class="fas fa-${statusIcon} me-2"></i>
            Companies House enrichment ${status.replace('_', ' ')}
            ${data.enrichment_source ? ` via ${data.enrichment_source}` : ''}
        </div>
    `;

    // Company Profile Section
    if (data.company_profile) {
        const profile = data.company_profile;
        html += `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        Company Profile
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Company Name:</strong> ${profile.company_name || 'N/A'}</p>
                            <p><strong>Company Number:</strong> ${profile.company_number || data.company_number || 'N/A'}</p>
                            <p><strong>Status:</strong> <span class="badge bg-${profile.company_status === 'active' ? 'success' : 'secondary'}">${profile.company_status || 'N/A'}</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Company Type:</strong> ${profile.company_type || 'N/A'}</p>
                            <p><strong>Incorporation Date:</strong> ${profile.date_of_creation || 'N/A'}</p>
                            ${profile.sic_codes ? `<p><strong>SIC Codes:</strong> ${profile.sic_codes.join(', ')}</p>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Registered Office Address Section
    if (data.registered_office_address) {
        const address = data.registered_office_address;
        html += `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Registered Office Address
                    </h6>
                </div>
                <div class="card-body">
                    <address class="mb-0">
                        ${address.address_line_1 || ''}<br>
                        ${address.address_line_2 ? address.address_line_2 + '<br>' : ''}
                        ${address.locality || ''} ${address.postal_code || ''}<br>
                        ${address.country || ''}
                    </address>
                </div>
            </div>
        `;
    }

    // Persons with Significant Control Section
    if (data.persons_with_significant_control && data.persons_with_significant_control.length > 0) {
        html += `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        Persons with Significant Control
                    </h6>
                </div>
                <div class="card-body">
        `;

        data.persons_with_significant_control.forEach((person, index) => {
            html += `
                <div class="${index > 0 ? 'border-top pt-3 mt-3' : ''}">
                    <p><strong>Name:</strong> ${person.name || 'N/A'}</p>
                    <p><strong>Nationality:</strong> ${person.nationality || 'N/A'}</p>
                    <p><strong>Country of Residence:</strong> ${person.country_of_residence || 'N/A'}</p>
                    ${person.natures_of_control ? `<p><strong>Nature of Control:</strong> ${person.natures_of_control.join(', ')}</p>` : ''}
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    }

    // Filing History Section
    if (data.filing_history && data.filing_history.length > 0) {
        html += `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Recent Filing History
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Category</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        data.filing_history.forEach(filing => {
            html += `
                <tr>
                    <td>${filing.date || 'N/A'}</td>
                    <td><span class="badge bg-info">${filing.category || 'N/A'}</span></td>
                    <td>${filing.description || 'N/A'}</td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    // Errors Section
    if (data.errors && data.errors.length > 0) {
        html += `
            <div class="card mb-3">
                <div class="card-header bg-warning">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Warnings
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
        `;

        data.errors.forEach(error => {
            html += `<li>${error}</li>`;
        });

        html += `
                    </ul>
                </div>
            </div>
        `;
    }

    // Timestamp
    if (data.timestamp) {
        html += `
            <div class="text-muted small">
                <i class="fas fa-clock me-1"></i>
                Enriched on ${new Date(data.timestamp).toLocaleString()}
            </div>
        `;
    }

    return html;
}

/**
 * Save current enrichment data
 * @param {string} moduleName - Name of the module
 */
async function saveCurrentEnrichmentData(moduleName) {
    try {
        const saveBtn = document.getElementById('save-enrichment-btn');

        if (!currentEnrichmentData || !currentEnrichmentData[moduleName]) {
            alert('No enrichment data to save. Please run enrichment first.');
            return;
        }

        // Show loading state
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Saving...';

        // Save enrichment data
        await saveEnrichmentData(currentBusinessId, moduleName, currentEnrichmentData[moduleName]);

        // Show success message
        const outputContainer = document.getElementById('enrichment-output-container');
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success mt-3';
        successAlert.innerHTML = '<i class="fas fa-check-circle me-2"></i>Enrichment data saved successfully!';
        outputContainer.appendChild(successAlert);

        // Reset save button
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-1"></i> Save Enrichment Data';

        // Hide save button after successful save
        setTimeout(() => {
            saveBtn.classList.add('d-none');
        }, 2000);

    } catch (error) {
        console.error('Error saving enrichment data:', error);

        const outputContainer = document.getElementById('enrichment-output-container');
        const errorAlert = document.createElement('div');
        errorAlert.className = 'alert alert-danger mt-3';
        errorAlert.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>Error saving enrichment data: ${error.message}`;
        outputContainer.appendChild(errorAlert);

        // Reset save button
        const saveBtn = document.getElementById('save-enrichment-btn');
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-1"></i> Save Enrichment Data';
    }
}
