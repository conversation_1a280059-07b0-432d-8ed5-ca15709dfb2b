// Businesses related functions

console.log('DEBUG: window.businessesApi at businesses.js load:', window.businessesApi);

// DOM elements
const businessesTable = document.getElementById('businesses-table');
const businessesList = document.getElementById('businesses-list');
const loadingIndicator = document.getElementById('loading-indicator');
const noResults = document.getElementById('no-results');
const prevPageBtn = document.getElementById('prev-page');
const nextPageBtn = document.getElementById('next-page');
const pageInfo = document.getElementById('page-info');
const searchForm = document.getElementById('search-form');
const businessDetail = document.getElementById('business-detail');
const businessesContainer = document.querySelector('.businesses-container');
const backToListBtn = document.getElementById('back-to-list');
const editBusinessForm = document.getElementById('edit-business-form');
const detailTitle = document.getElementById('detail-title');

let sortField = 'touched'; // Default sort by touched date
let sortDirection = 'desc'; // Most recent first
let resultsPerPage = 10;

// Pagination state
let currentPage = 1;
let totalPages = 1;
let currentFilters = {};

// Get filters from form
function getFiltersFromForm() {
    const filters = {};
    const keyword = document.getElementById('search-keyword').value;
    const country = document.getElementById('search-country').value;
    const city = document.getElementById('search-city').value;
    const minRating = document.getElementById('search-min-rating').value;
    const maxRating = document.getElementById('search-max-rating').value;
    const minRatings = document.getElementById('search-min-ratings').value;
    const maxRatings = document.getElementById('search-max-ratings').value;
    const websiteStatus = document.getElementById('search-website-status').value;
    const phoneStatus = document.getElementById('search-phone-status').value;

    if (keyword) filters.keyword = keyword;
    if (country) filters.country = country;
    if (city) filters.city = city;
    if (minRating) filters.minRating = minRating;
    if (maxRating) filters.maxRating = maxRating;
    if (minRatings) filters.minRatings = parseInt(minRatings);
    if (maxRatings) filters.maxRatings = parseInt(maxRatings);
    
    // Handle website status
    if (websiteStatus === 'has') {
        filters.hasWebsite = true;
    } else if (websiteStatus === 'no') {
        filters.hasWebsite = false;
    }
    
    // Handle phone status
    if (phoneStatus === 'has') {
        filters.hasPhone = true;
    } else if (phoneStatus === 'no') {
        filters.hasPhone = false;
    }

    return filters;
}

// Load businesses with pagination and filtering
async function loadBusinesses(page = 1) {
  try {
    console.log('Loading businesses:', { page });
    
    // Show loading indicator
    loadingIndicator.classList.remove('hidden');
    noResults.classList.add('hidden');
    businessesList.innerHTML = '';
    
    // Update state
    currentPage = page;
    currentFilters = getFiltersFromForm();
    // Add sorting and limit to filters
    const filtersWithSort = { ...currentFilters };
    if (sortField) {
      filtersWithSort.sortField = sortField;
      filtersWithSort.sortDirection = sortDirection;
    }
    const limit = resultsPerPage;
    console.log('Current filters:', filtersWithSort);
    
    // Fetch businesses
    console.log('Calling businessesApi.getBusinesses with:', { page, limit, filters: filtersWithSort });
    const data = await businessesApi.getBusinesses(page, limit, filtersWithSort);
    console.log('Raw API response:', data);
    
    // Hide loading indicator
    loadingIndicator.classList.add('hidden');
    
    // Check if data is undefined or null
    if (!data) {
      console.error('No data received from API');
      noResults.classList.remove('hidden');
      return;
    }
    
    // Update pagination
    if (data.pagination) {
      totalPages = data.pagination.pages;
      updatePagination(data.pagination);
    } else {
      console.warn('No pagination data received');
      totalPages = 1;
      updatePagination({ page: 1, pages: 1 });
    }
    
    // Check if no results
    if (!data.businesses || data.businesses.length === 0) {
      console.log('No businesses found');
      noResults.classList.remove('hidden');
      return;
    }
    
    console.log('Rendering businesses:', data.businesses);
    // Render businesses
    let businesses = data.businesses || [];
    if (sortField) {
      businesses = sortBusinesses(businesses, sortField, sortDirection);
    }
    renderBusinessesList(businesses);
  } catch (error) {
    console.error('Error loading businesses:', error);
    loadingIndicator.classList.add('hidden');
    showNotification('Failed to load businesses: ' + error.message, true);
  }
}

// Render businesses list
function renderBusinessesList(businesses) {
  businessesList.innerHTML = '';
  businesses.forEach(business => {
    const row = document.createElement('tr');
    row.setAttribute('data-id', business.id);
    row.innerHTML = `
      <td>${business.name || 'N/A'}</td>
      <td>${business.address || 'N/A'}</td>
      <td>${business.touched ? new Date(business.touched).toLocaleString() : 'N/A'}</td>
      <td>
        <span class="links-icons">
          ${business.website ?
            `<a href="${business.website}" target="_blank" class="icon-link" title="Visit Website"><i class="fas fa-globe"></i></a>` :
            `<span class="icon-link disabled" title="No Website"><i class="fas fa-globe"></i></span>`}
          ${business.phone ?
            `<a href="tel:${business.phone}" class="icon-link" title="Call Business"><i class="fas fa-phone"></i></a>` :
            `<span class="icon-link disabled" title="No Phone"><i class="fas fa-phone"></i></span>`}
          <a href="https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(business.address)}&query_place_id=${encodeURIComponent(business.id)}" target="_blank" class="icon-link" title="View on Google Maps"><i class="fas fa-map-marker-alt"></i></a>
        </span>
      </td>
      <td>${business.rating ? `${business.rating} / 5` : 'N/A'}</td>
      <td>${business.user_ratings_total || '0'}</td>
      <td>
        <button class="btn btn-small view-business" data-id="${business.id}">View</button>
      </td>
    `;
    businessesList.appendChild(row);
  });

  // Add event listeners to view buttons
  document.querySelectorAll('.view-business').forEach(button => {
    button.addEventListener('click', (e) => {
      const businessId = e.target.getAttribute('data-id');
      loadBusinessDetails(businessId);
    });
  });
}

// Sorting logic
function sortBusinesses(businesses, field, direction) {
  return businesses.slice().sort((a, b) => {
    let valA = a[field] || '';
    let valB = b[field] || '';
    if (field === 'rating' || field === 'user_ratings_total') {
      valA = parseFloat(valA) || 0;
      valB = parseFloat(valB) || 0;
    } else if (field === 'touched') {
      valA = valA ? new Date(valA).getTime() : 0;
      valB = valB ? new Date(valB).getTime() : 0;
    } else {
      valA = valA.toString().toLowerCase();
      valB = valB.toString().toLowerCase();
    }
    if (valA < valB) return direction === 'asc' ? -1 : 1;
    if (valA > valB) return direction === 'asc' ? 1 : -1;
    return 0;
  });
}

// Add event listeners to table headers for sorting
function setupTableSorting() {
  document.querySelectorAll('#businesses-table th.sortable').forEach(th => {
    th.addEventListener('click', () => {
      const field = th.getAttribute('data-sort');
      if (sortField === field) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        sortField = field;
        sortDirection = 'asc';
      }
      loadBusinesses(1);
    });
  });
}

document.addEventListener('DOMContentLoaded', () => {
  setupTableSorting();
  const resultsPerPageSelect = document.getElementById('results-per-page');
  if (resultsPerPageSelect) {
    resultsPerPageSelect.addEventListener('change', (e) => {
      resultsPerPage = parseInt(e.target.value, 10);
      loadBusinesses(1);
    });
  }
});

// Update pagination controls
function updatePagination(pagination) {
  // Update page info
  pageInfo.textContent = `Page ${pagination.page} of ${pagination.pages}`;
  
  // Update buttons state
  prevPageBtn.disabled = pagination.page <= 1;
  nextPageBtn.disabled = pagination.page >= pagination.pages;
}

// Load business details
async function loadBusinessDetails(businessId) {
  try {
    // Show loading
    showNotification('Loading business details...');
    
    // Fetch business
    const business = await businessesApi.getBusiness(businessId);
    
    // Update form
    document.getElementById('edit-business-id').value = business.id;
    document.getElementById('edit-name').value = business.name || '';
    document.getElementById('edit-address').value = business.address || '';
    document.getElementById('edit-phone').value = business.phone || '';
    document.getElementById('edit-website').value = business.website || '';
    document.getElementById('edit-rating').value = business.rating || '';
    document.getElementById('edit-user_ratings_total').value = business.user_ratings_total || '';
    document.getElementById('edit-keyword').value = business.keyword || '';
    document.getElementById('edit-country').value = business.country || '';
    document.getElementById('edit-city').value = business.city || '';
    document.getElementById('edit-locality').value = business.locality || '';
    
    // Ensure field editability is updated after loading
    if (typeof updateEditBusinessFormFields === 'function') {
      updateEditBusinessFormFields();
    }
    
    // Update title
    detailTitle.textContent = business.name;
    
    // Show detail view
    showBusinessDetail();
    
    // Join socket room for real-time updates
    joinBusinessRoom(businessId);
    
    // Load notes
    loadBusinessNotes(businessId);
  } catch (error) {
    console.error('Error loading business details:', error);
    showNotification('Failed to load business details', true);
  }
}

// Show business list
function showBusinessList() {
  businessesContainer.classList.remove('hidden');
  businessDetail.classList.add('hidden');
  
  // Leave socket room if needed
  const businessId = document.getElementById('edit-business-id').value;
  if (businessId) {
    leaveBusinessRoom(businessId);
  }
}

// Show business detail
function showBusinessDetail() {
  businessesContainer.classList.add('hidden');
  businessDetail.classList.remove('hidden');
}

// Update business in list
function updateBusinessInList(businessId, business) {
  const row = businessesList.querySelector(`tr[data-id="${businessId}"]`);
  if (row) {
    row.innerHTML = `
      <td>${business.name || 'N/A'}</td>
      <td>${business.address || 'N/A'}</td>
      <td>${business.touched ? new Date(business.touched).toLocaleString() : 'N/A'}</td>
      <td>
        <span class="links-icons">
          ${business.website ?
            `<a href="${business.website}" target="_blank" class="icon-link" title="Visit Website"><i class="fas fa-globe"></i></a>` :
            `<span class="icon-link disabled" title="No Website"><i class="fas fa-globe"></i></span>`}
          ${business.phone ?
            `<a href="tel:${business.phone}" class="icon-link" title="Call Business"><i class="fas fa-phone"></i></a>` :
            `<span class="icon-link disabled" title="No Phone"><i class="fas fa-phone"></i></span>`}
          <a href="https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(business.address)}&query_place_id=${encodeURIComponent(business.id)}" target="_blank" class="icon-link" title="View on Google Maps"><i class="fas fa-map-marker-alt"></i></a>
        </span>
      </td>
      <td>${business.rating ? `${business.rating} / 5` : 'N/A'}</td>
      <td>${business.user_ratings_total || '0'}</td>
      <td>
        <button class="btn btn-small view-business" data-id="${businessId}">View</button>
      </td>
    `;
    
    // Re-add event listener
    row.querySelector('.view-business').addEventListener('click', (e) => {
      loadBusinessDetails(businessId);
    });
  }
}

// Remove business from list
function removeBusinessFromList(businessId) {
  const row = businessesList.querySelector(`tr[data-id="${businessId}"]`);
  if (row) {
    row.remove();
    
    // Check if list is empty
    if (businessesList.children.length === 0) {
      noResults.classList.remove('hidden');
    }
  }
}

// Update business details
function updateBusinessDetails(businessId, business) {
  const currentBusinessId = document.getElementById('edit-business-id').value;
  
  if (currentBusinessId === businessId) {
    document.getElementById('edit-name').value = business.name || '';
    document.getElementById('edit-address').value = business.address || '';
    document.getElementById('edit-phone').value = business.phone || '';
    document.getElementById('edit-website').value = business.website || '';
    document.getElementById('edit-rating').value = business.rating || '';
    
    // Update title
    detailTitle.textContent = business.name;
  }
}

// Event listeners
prevPageBtn.addEventListener('click', () => {
  if (currentPage > 1) {
    loadBusinesses(currentPage - 1);
  }
});

nextPageBtn.addEventListener('click', () => {
  if (currentPage < totalPages) {
    loadBusinesses(currentPage + 1);
  }
});

searchForm.addEventListener('submit', (e) => {
  e.preventDefault();
  loadBusinesses(1);
});

searchForm.addEventListener('reset', () => {
  // Clear filters and reload
  setTimeout(() => {
    loadBusinesses(1);
  }, 0);
});

backToListBtn.addEventListener('click', () => {
  showBusinessList();
});

editBusinessForm.addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const businessId = document.getElementById('edit-business-id').value;
  const formData = new FormData(editBusinessForm);
  
  const businessData = {
    name: formData.get('name'),
    address: formData.get('address'),
    phone: formData.get('phone'),
    website: formData.get('website'),
    rating: formData.get('rating'),
    user_ratings_total: formData.get('user_ratings_total'),
    keyword: formData.get('keyword'),
    country: formData.get('country'),
    city: formData.get('city'),
    locality: formData.get('locality')
  };
  
  try {
    showNotification('Saving changes...');
    
    await businessesApi.updateBusiness(businessId, businessData);
    
    showNotification('Business updated successfully');
  } catch (error) {
    console.error('Error updating business:', error);
    showNotification('Failed to update business', true);
  }
});
