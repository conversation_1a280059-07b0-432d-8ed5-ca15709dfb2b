const {
  client,
  getAsync,
  setAsync,
  delAsync,
  keysAsync,
  hgetallAsync,
  hsetAsync,
  hmsetAsync,
  hdelAsync,
  existsAsync,
  scanAsync,
  saddAsync,
  smembersAsync,
  sremAsync
} = require('../config/redis');

// Business prefix in Redis
const BUSINESS_PREFIX = 'business:';
// Notes prefix in Redis
const NOTE_PREFIX = 'note:';
// Business notes index prefix
const BUSINESS_NOTES_PREFIX = 'business_notes:';

// Helper function to get IDs without website
async function getIdsWithoutWebsite(websiteIds) {
  const allKeys = await keysAsync(`${BUSINESS_PREFIX}*`);
  const allIds = allKeys.map(key => key.replace(BUSINESS_PREFIX, ''));
  return allIds.filter(id => !websiteIds.includes(id));
}

// Helper function to get IDs without phone
async function getIdsWithoutPhone(phoneIds) {
  const allKeys = await keysAsync(`${BUSINESS_PREFIX}*`);
  const allIds = allKeys.map(key => key.replace(BUSINESS_PREFIX, ''));
  return allIds.filter(id => !phoneIds.includes(id));
}

/**
 * Get all businesses with pagination and filtering
 */
const getBusinesses = async ({ page = 1, limit = 10, filters = {} }) => {
  try {
    console.log('getBusinesses called with:', { page, limit, filters });
    
    let placesIds = new Set();
    let isFirstFilter = true;
    const {
      keyword,
      country,
      city,
      minRating,
      maxRating,
      minRatings,
      maxRatings,
      hasWebsite,
      hasPhone
    } = filters;

    // If no keyword/location filters, get all business IDs
    if (isFirstFilter && !keyword && !country && !city) {
      console.log('No keyword/location filters applied, getting all businesses');
      const allKeys = await keysAsync(`${BUSINESS_PREFIX}*`);
      console.log(`Found ${allKeys.length} total businesses`);
      placesIds = new Set(allKeys.map(key => key.replace(BUSINESS_PREFIX, '')));
      isFirstFilter = false;
    }

    // Apply keyword filter
    if (keyword) {
      console.log('Applying keyword filter:', keyword);
      const keywordIds = await smembersAsync(`index:keyword:${keyword.toLowerCase()}`);
      console.log(`Found ${keywordIds.length} businesses matching keyword`);
      if (isFirstFilter) {
        placesIds = new Set(keywordIds);
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => keywordIds.includes(id)));
      }
    }

    // Apply country filter
    if (country) {
      console.log('Applying country filter:', country);
      const countryIds = await smembersAsync(`index:country:${country.toLowerCase()}`);
      console.log(`Found ${countryIds.length} businesses in country`);
      if (isFirstFilter) {
        placesIds = new Set(countryIds);
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => countryIds.includes(id)));
      }
    }

    // Apply city filter
    if (city) {
      console.log('Applying city filter:', city);
      const cityIds = await smembersAsync(`index:city:${city.toLowerCase()}`);
      console.log(`Found ${cityIds.length} businesses in city`);
      if (isFirstFilter) {
        placesIds = new Set(cityIds);
        isFirstFilter = false;
      } else {
        placesIds = new Set([...placesIds].filter(id => cityIds.includes(id)));
      }
    }

    // Apply website status filter
    if (hasWebsite !== undefined) {
      console.log('Applying website filter:', hasWebsite);
      const websiteIds = await smembersAsync('index:has_website');
      if (isFirstFilter) {
        placesIds = new Set(hasWebsite ? websiteIds : await getIdsWithoutWebsite(websiteIds));
        isFirstFilter = false;
      } else {
        if (hasWebsite) {
          placesIds = new Set([...placesIds].filter(id => websiteIds.includes(id)));
        } else {
          const noWebsiteIds = await getIdsWithoutWebsite(websiteIds);
          placesIds = new Set([...placesIds].filter(id => noWebsiteIds.includes(id)));
        }
      }
    }

    // Apply phone status filter
    if (hasPhone !== undefined) {
      console.log('Applying phone filter:', hasPhone);
      const phoneIds = await smembersAsync('index:has_phone');
      if (isFirstFilter) {
        placesIds = new Set(hasPhone ? phoneIds : await getIdsWithoutPhone(phoneIds));
        isFirstFilter = false;
      } else {
        if (hasPhone) {
          placesIds = new Set([...placesIds].filter(id => phoneIds.includes(id)));
        } else {
          const noPhoneIds = await getIdsWithoutPhone(phoneIds);
          placesIds = new Set([...placesIds].filter(id => noPhoneIds.includes(id)));
        }
      }
    }
    
    // Get business details and apply rating filters before pagination
    const filteredBusinesses = [];
    const idsArray = Array.from(placesIds);
    console.log(`Total businesses before rating filters: ${idsArray.length}`);
    
    for (const id of idsArray) {
      const business = await hgetallAsync(`${BUSINESS_PREFIX}${id}`);
      if (business) {
        try {
          business.id = id;
          // Apply rating filters
          if (minRating !== undefined && (!business.rating || parseFloat(business.rating) < minRating)) continue;
          if (maxRating !== undefined && (!business.rating || parseFloat(business.rating) > maxRating)) continue;
          if (minRatings !== undefined && (!business.user_ratings_total || parseInt(business.user_ratings_total) < minRatings)) continue;
          if (maxRatings !== undefined && (!business.user_ratings_total || parseInt(business.user_ratings_total) > maxRatings)) continue;

          // Compute 'touched' field: most recent of created_at, updated_at, or any note's createdAt/updatedAt
          let touched = null;
          let touchedDate = 0;
          // Prefer updated_at, then created_at, fallback to 0
          if (business.updated_at && !isNaN(Date.parse(business.updated_at))) {
            touched = business.updated_at;
            touchedDate = new Date(business.updated_at).getTime();
          } else if (business.created_at && !isNaN(Date.parse(business.created_at))) {
            touched = business.created_at;
            touchedDate = new Date(business.created_at).getTime();
          }
          // Use getNotesByBusinessId helper for efficiency
          let latestNoteDate = null;
          try {
            const notes = await module.exports.getNotesByBusinessId(id);
            for (const note of notes) {
              // Only consider valid dates
              const noteDates = [note.createdAt, note.updatedAt].filter(d => d && !isNaN(Date.parse(d))).map(d => new Date(d).getTime());
              for (const d of noteDates) {
                if (!latestNoteDate || d > latestNoteDate) latestNoteDate = d;
              }
            }
          } catch (e) {
            // If notes fetch fails, ignore
          }
          if (latestNoteDate && latestNoteDate > touchedDate) {
            touched = new Date(latestNoteDate).toISOString();
            touchedDate = latestNoteDate;
          }
          // If still no valid date, fallback to now
          if (!touched) {
            touched = new Date().toISOString();
          }
          business.touched = touched;

          filteredBusinesses.push(business);
        } catch (error) {
          console.error(`Error parsing business data for ID ${id}:`, error);
          console.error('Raw business data:', business);
        }
      }
    }

    console.log(`Total businesses after rating filters: ${filteredBusinesses.length}`);
    
    // Apply pagination to filtered results
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedBusinesses = filteredBusinesses.slice(startIndex, endIndex);
    console.log(`Returning businesses ${startIndex + 1} to ${endIndex}`);

    return {
      businesses: paginatedBusinesses,
      pagination: {
        page,
        pages: Math.ceil(filteredBusinesses.length / limit),
        total: filteredBusinesses.length
      }
    };
  } catch (err) {
    console.error('Error in getBusinesses:', err);
    throw err;
  }
};

/**
 * Get a business by ID
 */
const getBusinessById = async (id) => {
  try {
    const business = await hgetallAsync(`${BUSINESS_PREFIX}${id}`);
    
    if (!business) {
      return null;
    }
    
    // Add ID to business object
    business.id = id;
    
    return business;
  } catch (error) {
    console.error(`Error getting business ${id}:`, error);
    throw error;
  }
};

/**
 * Update a business
 */
const updateBusiness = async (id, businessData) => {
  try {
    // Sanitize and coerce all fields
    const sanitized = {
      name: businessData.name || '',
      address: businessData.address || '',
      phone: businessData.phone || '',
      website: businessData.website || '',
      rating: businessData.rating === undefined || businessData.rating === null || businessData.rating === '' ? '' : String(Number(businessData.rating)),
      user_ratings_total: businessData.user_ratings_total === undefined || businessData.user_ratings_total === null || businessData.user_ratings_total === '' ? '' : String(Number(businessData.user_ratings_total)),
      keyword: businessData.keyword || '',
      country: businessData.country || '',
      city: businessData.city || '',
      locality: businessData.locality || ''
    };

    // Check if business exists
    const exists = await existsAsync(`${BUSINESS_PREFIX}${id}`);
    if (!exists) {
      throw new Error(`Business ${id} not found`);
    }

    // Get existing business data to update indices
    const oldBusiness = await hgetallAsync(`${BUSINESS_PREFIX}${id}`);

    // Remove from old indices if values changed
    if (oldBusiness.keyword && oldBusiness.keyword !== sanitized.keyword) {
      await sremAsync(`index:keyword:${oldBusiness.keyword.toLowerCase()}`, id);
    }
    if (oldBusiness.country && oldBusiness.country !== sanitized.country) {
      await sremAsync(`index:country:${oldBusiness.country.toLowerCase()}`, id);
    }
    if (oldBusiness.city && oldBusiness.city !== sanitized.city) {
      await sremAsync(`index:city:${oldBusiness.city.toLowerCase()}`, id);
    }
    if (oldBusiness.rating && oldBusiness.rating !== sanitized.rating) {
      const oldRating = Math.floor(parseFloat(oldBusiness.rating));
      if (!isNaN(oldRating)) {
        await sremAsync(`index:rating:${oldRating}`, id);
      }
    }
    if (oldBusiness.website && !sanitized.website) {
      await sremAsync('index:has_website', id);
    }
    if (oldBusiness.phone && !sanitized.phone) {
      await sremAsync('index:has_phone', id);
    }

    // Add to new indices
    if (sanitized.keyword) {
      await saddAsync(`index:keyword:${sanitized.keyword.toLowerCase()}`, id);
    }
    if (sanitized.country) {
      await saddAsync(`index:country:${sanitized.country.toLowerCase()}`, id);
    }
    if (sanitized.city) {
      await saddAsync(`index:city:${sanitized.city.toLowerCase()}`, id);
    }
    if (sanitized.rating) {
      const newRating = Math.floor(parseFloat(sanitized.rating));
      if (!isNaN(newRating)) {
        await saddAsync(`index:rating:${newRating}`, id);
      }
    }
    if (sanitized.website) {
      await saddAsync('index:has_website', id);
    }
    if (sanitized.phone) {
      await saddAsync('index:has_phone', id);
    }

    // Update business data
    await hmsetAsync(`${BUSINESS_PREFIX}${id}`, sanitized);
    return sanitized;
  } catch (error) {
    console.error(`Error updating business ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a business
 */
const deleteBusiness = async (id) => {
  try {
    // Check if business exists and get data for index cleanup
    const business = await hgetallAsync(`${BUSINESS_PREFIX}${id}`);
    
    if (!business) {
      throw new Error(`Business ${id} not found`);
    }

    // Remove from indices
    if (business.keyword) {
      await sremAsync(`index:keyword:${business.keyword.toLowerCase()}`, id);
    }
    if (business.country) {
      await sremAsync(`index:country:${business.country.toLowerCase()}`, id);
    }
    if (business.city) {
      await sremAsync(`index:city:${business.city.toLowerCase()}`, id);
    }
    if (business.rating) {
      await sremAsync(`index:rating:${Math.floor(parseFloat(business.rating))}`, id);
    }
    if (business.website) {
      await sremAsync('index:has_website', id);
    }
    if (business.phone) {
      await sremAsync('index:has_phone', id);
    }
    
    // Delete business
    await delAsync(`${BUSINESS_PREFIX}${id}`);
    
    // Delete associated notes
    const noteKeys = await keysAsync(`${NOTE_PREFIX}*`);
    
    for (const noteKey of noteKeys) {
      const note = await hgetallAsync(noteKey);
      
      if (note && note.businessId === id) {
        await delAsync(noteKey);
      }
    }
    
    // Delete business notes index
    await delAsync(`${BUSINESS_NOTES_PREFIX}${id}`);
    
    return true;
  } catch (error) {
    console.error(`Error deleting business ${id}:`, error);
    throw error;
  }
};

/**
 * Get all notes for a business
 */
const getNotesByBusinessId = async (businessId) => {
  try {
    // Check if business exists
    const exists = await existsAsync(`${BUSINESS_PREFIX}${businessId}`);
    
    if (!exists) {
      throw new Error(`Business ${businessId} not found`);
    }
    
    // Get all note keys
    const noteKeys = await keysAsync(`${NOTE_PREFIX}*`);
    const notes = [];
    
    // Get notes for business
    for (const noteKey of noteKeys) {
      const note = await hgetallAsync(noteKey);
      
      if (note && note.businessId === businessId) {
        // Add ID to note object
        note.id = noteKey.replace(NOTE_PREFIX, '');
        notes.push(note);
      }
    }
    
    // Sort notes by creation date (newest first)
    notes.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    return notes;
  } catch (error) {
    console.error(`Error getting notes for business ${businessId}:`, error);
    throw error;
  }
};

/**
 * Get a note by ID
 */
const getNoteById = async (id) => {
  try {
    const note = await hgetallAsync(`${NOTE_PREFIX}${id}`);
    
    if (!note) {
      return null;
    }
    
    // Add ID to note object
    note.id = id;
    
    return note;
  } catch (error) {
    console.error(`Error getting note ${id}:`, error);
    throw error;
  }
};

/**
 * Add a new note
 */
const addNote = async (noteData) => {
  try {
    // Check if business exists
    const exists = await existsAsync(`${BUSINESS_PREFIX}${noteData.businessId}`);
    
    if (!exists) {
      throw new Error(`Business ${noteData.businessId} not found`);
    }
    
    // Add note
    await hmsetAsync(`${NOTE_PREFIX}${noteData.id}`, noteData);
    
    // Add to business notes index
    await setAsync(`${BUSINESS_NOTES_PREFIX}${noteData.businessId}:${noteData.id}`, '1');
    
    return noteData;
  } catch (error) {
    console.error('Error adding note:', error);
    throw error;
  }
};

/**
 * Update a note
 */
const updateNote = async (id, noteData) => {
  try {
    // Check if note exists
    const exists = await existsAsync(`${NOTE_PREFIX}${id}`);
    
    if (!exists) {
      throw new Error(`Note ${id} not found`);
    }
    
    // Update note
    await hmsetAsync(`${NOTE_PREFIX}${id}`, noteData);
    
    return noteData;
  } catch (error) {
    console.error(`Error updating note ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a note
 */
const deleteNote = async (id) => {
  try {
    // Get note to check business ID
    const note = await hgetallAsync(`${NOTE_PREFIX}${id}`);
    
    if (!note) {
      throw new Error(`Note ${id} not found`);
    }
    
    // Delete note
    await delAsync(`${NOTE_PREFIX}${id}`);
    
    // Remove from business notes index
    await delAsync(`${BUSINESS_NOTES_PREFIX}${note.businessId}:${id}`);
    
    return true;
  } catch (error) {
    console.error(`Error deleting note ${id}:`, error);
    throw error;
  }
};

module.exports = {
  getBusinesses,
  getBusinessById,
  updateBusiness,
  deleteBusiness,
  getNotesByBusinessId,
  getNoteById,
  addNote,
  updateNote,
  deleteNote
};
