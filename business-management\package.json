{"name": "business-management", "version": "1.0.0", "description": "Business Management Dashboard for managing business data stored in Redis", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "jsonwebtoken": "^8.5.1", "passport": "^0.4.1", "passport-jwt": "^4.0.0", "redis": "^3.1.2", "socket.io": "^4.4.1"}, "devDependencies": {"nodemon": "^2.0.15"}}