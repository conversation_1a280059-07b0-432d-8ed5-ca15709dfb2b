import React, { useState } from 'react';
import './SearchForm.css';

const SearchForm = ({ onSubmit, isSearching }) => {
  const [formData, setFormData] = useState({
    keyword: '',
    country: '',
    city: '',
    locality: '', // New field for more granular location
    minRating: 0,
    hasWebsite: false,
    noWebsite: false, // New field for filtering without website
    rateLimit: 5,
    maxResults: 100
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="search-form-container">
      <h2>Business Finder</h2>
      <form onSubmit={handleSubmit} className="search-form">
        <div className="form-group">
          <label htmlFor="keyword">Keyword *</label>
          <input
            type="text"
            id="keyword"
            name="keyword"
            value={formData.keyword}
            onChange={handleChange}
            placeholder="Enter business type or name"
            required
            disabled={isSearching}
          />
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="country">Country</label>
            <input
              type="text"
              id="country"
              name="country"
              value={formData.country}
              onChange={handleChange}
              placeholder="Country"
              disabled={isSearching}
            />
          </div>

          <div className="form-group">
            <label htmlFor="city">City</label>
            <input
              type="text"
              id="city"
              name="city"
              value={formData.city}
              onChange={handleChange}
              placeholder="City"
              disabled={isSearching}
            />
          </div>
          <div className="form-group">
            <label htmlFor="locality">Locality</label>
            <input
              type="text"
              id="locality"
              name="locality"
              value={formData.locality}
              onChange={handleChange}
              placeholder="Neighborhood, District, etc."
              disabled={isSearching}
            />
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="minRating">
            Minimum Rating: {formData.minRating}
          </label>
          <input
            type="range"
            id="minRating"
            name="minRating"
            min="0"
            max="5"
            step="0.5"
            value={formData.minRating}
            onChange={handleChange}
            disabled={isSearching}
          />
        </div>

        <div className="form-group checkbox-group">
          <input
            type="checkbox"
            id="hasWebsite"
            name="hasWebsite"
            checked={formData.hasWebsite}
            onChange={handleChange}
            disabled={isSearching}
          />
          <label htmlFor="hasWebsite">Has Website</label>
          <input
            type="checkbox"
            id="noWebsite"
            name="noWebsite"
            checked={formData.noWebsite}
            onChange={handleChange}
            disabled={isSearching}
            style={{ marginLeft: '20px' }}
          />
          <label htmlFor="noWebsite">No Website</label>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="rateLimit">Rate Limit (seconds)</label>
            <input
              type="number"
              id="rateLimit"
              name="rateLimit"
              min="1"
              max="60"
              value={formData.rateLimit}
              onChange={handleChange}
              disabled={isSearching}
            />
          </div>

          <div className="form-group">
            <label htmlFor="maxResults">Max Results</label>
            <input
              type="number"
              id="maxResults"
              name="maxResults"
              min="1"
              max="200"
              value={formData.maxResults}
              onChange={handleChange}
              disabled={isSearching}
            />
          </div>
        </div>

        <button 
          type="submit" 
          className="search-button" 
          disabled={isSearching || !formData.keyword}
        >
          {isSearching ? 'Searching...' : 'Search'}
        </button>
      </form>
    </div>
  );
};

export default SearchForm;
