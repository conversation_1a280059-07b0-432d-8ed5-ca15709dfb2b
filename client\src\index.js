import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import { SocketProvider } from './services/socket';
import { SearchProvider } from './services/searchContext';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <SocketProvider>
      <SearchProvider>
        <App />
      </SearchProvider>
    </SocketProvider>
  </React.StrictMode>
);
