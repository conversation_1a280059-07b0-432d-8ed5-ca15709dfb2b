services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped

  backend:
    build:
      context: ./server
    ports:
      - "3003:3003"
    environment:
      - PORT=3003
      - CLIENT_URL=http://localhost:3005
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    restart: unless-stopped

  frontend:
    build:
      context: ./client
    ports:
      - "3005:3005"
    environment:
      - REACT_APP_API_URL=http://localhost:3003
    depends_on:
      - backend
    restart: unless-stopped

  business-management:
    build:
      context: ./business-management
    ports:
      - "3004:3004"
    environment:
      - PORT=3004
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your_jwt_secret_here_make_sure_to_edit_this_for_production
    depends_on:
      - redis
    restart: unless-stopped

  business_connector:
    build:
      context: ./business-connector
      dockerfile: Dockerfile
    env_file:
      - ./business-connector/.env
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      # You can keep HUNTER_API_KEY here if you want to allow overriding from the main .env or shell environment,
      # but the value from env_file will take precedence if defined there.
      # If HUNTER_API_KEY is exclusively in business-connector/.env, you can remove the line below.
      - HUNTER_API_KEY=${HUNTER_API_KEY}
    ports:
      - "3006:3006"
    depends_on:
      - redis

volumes:
  redis-data: