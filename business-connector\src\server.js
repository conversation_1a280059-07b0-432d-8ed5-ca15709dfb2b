const express = require('express');
const cors = require('cors');
const path = require('path');
const { createRedisClient } = require('./config/redis');
const routes = require('./routes');
const errorHandler = require('./middleware/errorHandler');
const { logger } = require('./services/logService');
const { initializeModules } = require('./modules');

// Initialize Express app
const app = express();
const server = require('http').createServer(app);

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Initialize Redis client
const redisClient = createRedisClient();

// Initialize modules
initializeModules().then(() => {
  logger.info('Modules initialized successfully');
}).catch((error) => {
  logger.error('Error initializing modules:', error);
});

// Make Redis client available to routes
app.use((req, res, next) => {
  req.redisClient = redisClient;
  next();
});

// API routes
app.use('/api', routes);

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling middleware
app.use(errorHandler);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

module.exports = { app, server };
