const { initializeApp } = require('./app');

const PORT = process.env.PORT || 3003;

// Start the server
const startServer = async () => {
  try {
    const { server } = await initializeApp();
    
    server.listen(PORT, '0.0.0.0', () => {
      console.log(`Server running on http://0.0.0.0:${PORT}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
